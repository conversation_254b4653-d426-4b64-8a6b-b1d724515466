/**
 * iframe监听脚本 - 用于在iframe内部监听用户行为
 * 这个脚本会被注入到iframe中，监听用户的交互行为并通过postMessage发送到父页面
 */

(function() {
  'use strict';

  // 防止重复注入
  if (window.foreseeIframeListenerInjected) {
    console.log('🖼️ iframe监听脚本已存在，跳过注入');
    return;
  }
  window.foreseeIframeListenerInjected = true;

  console.log('🖼️ iframe监听脚本开始初始化...');

  // 生成唯一的iframe标识
  const iframeId = 'iframe_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  
  // 节流控制
  let lastActionTime = 0;
  const actionThrottleDelay = 300;

  /**
   * 发送消息到父页面
   * @param {Object} data 要发送的数据
   */
  function sendMessageToParent(data) {
    try {
      window.parent.postMessage({
        source: 'FORESEE_IFRAME_TRACKER',
        data: data
      }, '*');
    } catch (error) {
      console.error('🖼️ 发送消息到父页面失败:', error);
    }
  }

  /**
   * 节流函数
   * @param {Function} func 要执行的函数
   * @param {number} delay 延迟时间
   */
  function throttle(func, delay) {
    return function(...args) {
      const now = Date.now();
      if (now - lastActionTime >= delay) {
        lastActionTime = now;
        func.apply(this, args);
      }
    };
  }

  /**
   * 获取元素信息
   * @param {Element} element 目标元素
   * @returns {Object} 元素信息
   */
  function getElementInfo(element) {
    return {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      text: element.textContent?.substring(0, 50) || '',
      type: element.type || '',
      placeholder: element.placeholder || '',
      value: element.value || ''
    };
  }

  /**
   * 检查元素是否重要
   * @param {Element} element 目标元素
   * @returns {boolean} 是否重要
   */
  function isImportantElement(element) {
    const importantTags = ['button', 'a', 'input', 'select', 'textarea'];
    const tagName = element.tagName.toLowerCase();

    // 基本标签检查
    if (importantTags.includes(tagName)) {
      return true;
    }

    // 检查是否有点击事件
    if (element.onclick !== null) {
      return true;
    }

    // 检查role属性
    const role = element.getAttribute('role');
    if (role === 'button' || role === 'textbox' || role === 'link') {
      return true;
    }

    // 检查contenteditable属性
    if (element.contentEditable === 'true') {
      return true;
    }

    // 检查是否包含重要的子元素（如图标）
    if (element.querySelector('svg') || element.querySelector('.icon')) {
      return true;
    }

    // 检查文本内容是否包含重要关键词
    const text = element.textContent?.trim().toLowerCase() || '';
    const importantKeywords = ['发送', 'send', '运行', 'run', '执行', 'execute', '提交', 'submit', '确认', 'confirm'];
    if (importantKeywords.some(keyword => text.includes(keyword))) {
      return true;
    }

    return false;
  }

  /**
   * 处理点击事件
   * @param {Event} event 点击事件
   */
  const handleClick = throttle(function(event) {
    const target = event.target;

    // 只处理重要的点击事件
    if (!isImportantElement(target)) {
      return;
    }

    console.log('🖼️ iframe内检测到重要点击事件:', target);

    const actionData = {
      type: 'iframe_click',
      iframeId: iframeId,
      target: getElementInfo(target),
      coordinates: { x: event.clientX, y: event.clientY },
      timestamp: Date.now(),
      url: window.location.href,
      description: `用户在iframe中点击了${target.tagName}元素`
    };

    sendMessageToParent(actionData);
  }, actionThrottleDelay);

  /**
   * 处理输入事件
   * @param {Event} event 输入事件
   */
  const handleInput = throttle(function(event) {
    const target = event.target;
    const value = target.value || target.textContent || '';

    // 只处理有意义的输入
    if (!value || value.length < 2) {
      return;
    }

    console.log('🖼️ iframe内检测到输入事件:', target);

    const actionData = {
      type: 'iframe_input',
      iframeId: iframeId,
      target: getElementInfo(target),
      value: value.substring(0, 100), // 限制长度
      timestamp: Date.now(),
      url: window.location.href,
      description: `用户在iframe的${target.tagName}中输入了内容`
    };

    sendMessageToParent(actionData);
  }, actionThrottleDelay * 2); // 输入事件使用更长的节流时间

  /**
   * 处理键盘事件
   * @param {Event} event 键盘事件
   */
  const handleKeydown = throttle(function(event) {
    // 只记录重要的键盘事件
    const importantKeys = ['Enter', 'Tab', 'Escape', 'Space'];
    if (!importantKeys.includes(event.key)) {
      return;
    }

    console.log('🖼️ iframe内检测到重要键盘事件:', event.key);

    const actionData = {
      type: 'iframe_keyboard',
      iframeId: iframeId,
      target: getElementInfo(event.target),
      key: event.key,
      timestamp: Date.now(),
      url: window.location.href,
      description: `用户在iframe中按下了${event.key}键`
    };

    sendMessageToParent(actionData);
  }, actionThrottleDelay);

  /**
   * 处理表单提交事件
   * @param {Event} event 提交事件
   */
  function handleSubmit(event) {
    const form = event.target;
    console.log('🖼️ iframe内检测到表单提交:', form);

    const actionData = {
      type: 'iframe_submit',
      iframeId: iframeId,
      target: getElementInfo(form),
      timestamp: Date.now(),
      url: window.location.href,
      description: '用户在iframe中提交了表单'
    };

    sendMessageToParent(actionData);
  }

  /**
   * 设置事件监听器
   */
  function setupEventListeners() {
    console.log('🖼️ 设置iframe事件监听器');

    // 监听点击事件
    document.addEventListener('click', handleClick, { passive: true, capture: true });

    // 监听输入事件
    document.addEventListener('input', handleInput, { passive: true });

    // 监听键盘事件
    document.addEventListener('keydown', handleKeydown, { passive: true });

    // 监听表单提交事件
    document.addEventListener('submit', handleSubmit, { passive: true });

    console.log('🖼️ iframe事件监听器设置完成');
  }

  /**
   * 监听来自父页面的消息
   */
  function setupMessageListener() {
    window.addEventListener('message', function(event) {
      try {
        if (event.data && event.data.source === 'FORESEE_SCRIPT_INJECTION') {
          console.log('🖼️ 收到脚本注入消息:', event.data);
          // 可以在这里处理来自父页面的指令
        }
      } catch (error) {
        console.error('🖼️ 处理父页面消息失败:', error);
      }
    });
  }

  /**
   * 初始化iframe监听器
   */
  function init() {
    console.log('🖼️ 初始化iframe监听器, ID:', iframeId);

    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
        setupMessageListener();
      });
    } else {
      setupEventListeners();
      setupMessageListener();
    }

    // 通知父页面脚本已就绪
    sendMessageToParent({
      type: 'script_ready',
      iframeId: iframeId,
      url: window.location.href,
      timestamp: Date.now()
    });

    console.log('🖼️ iframe监听器初始化完成');
  }

  // 启动初始化
  init();

})();
