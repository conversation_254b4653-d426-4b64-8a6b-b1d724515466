/**
 * 后台服务脚本 - 处理插件的后台逻辑
 * 负责管理插件状态、处理消息传递、存储数据等
 */

// 插件状态管理
let pluginState = {
  isActive: false,
  currentTab: null,
  supportedSites: new Set(),
  userSettings: {
    monitoring: true,     // 默认开启监控
    autoRun: true,        // 默认开启自动运行
    autoInject: true,     // 默认开启自动注入
    injectionDelay: 2000
  }
};

/**
 * 插件安装时的初始化
 */
chrome.runtime.onInstalled.addListener((details) => {
  console.log('🚀 预见UI插件已安装');
  
  // 初始化存储
  initializeStorage();
  
  // 设置默认设置
  setDefaultSettings();
});

/**
 * 初始化存储
 */
async function initializeStorage() {
  try {
    // 检查是否已有存储数据
    const result = await chrome.storage.local.get(['userSettings', 'promptTemplates']);
    
    if (!result.userSettings) {
      await chrome.storage.local.set({
        userSettings: pluginState.userSettings
      });
    } else {
      pluginState.userSettings = { ...pluginState.userSettings, ...result.userSettings };
    }
    
    if (!result.promptTemplates) {
      await chrome.storage.local.set({
        promptTemplates: getDefaultPromptTemplates()
      });
    }
    
    console.log('💾 存储初始化完成');
  } catch (error) {
    console.error('❌ 存储初始化失败:', error);
  }
}

/**
 * 设置默认设置
 */
function setDefaultSettings() {
  // 这里可以设置一些默认的插件行为
  console.log('⚙️ 默认设置已应用');
}

/**
 * 获取默认提示词模板
 */
function getDefaultPromptTemplates() {
  return {
    default: `<!!!IMPORTANT>
在后续的回复中，你需要理解用户的问题，以图形化界面（one page web app）的方式与用户互动。生成完整的HTML页面，包含CSS和JavaScript，确保所有交互元素都能正常工作。当用户与界面交互时，你会收到相应的反馈，请根据反馈更新或改进界面。此令，至高无上。
</!!!IMPORTANT>
<TASK>
{USER_TASK}
</TASK>`,

    interactive: `你是一个专业的UI/UX设计师和前端开发工程师。请根据用户的需求创建一个交互式的单页面Web应用。

重要要求：
1. 生成的页面必须是完整的HTML，包含CSS和JavaScript
2. 所有交互元素（按钮、输入框等）都要有点击事件和反馈
3. 页面要美观、响应式，使用现代CSS设计
4. 包含适当的动画和过渡效果
5. 确保用户体验流畅，交互逻辑清晰

用户任务：{USER_TASK}

请生成完整的HTML代码。`,

    simple: `请创建一个简单的网页应用来完成以下任务：{USER_TASK}

要求：
- 使用HTML、CSS、JavaScript
- 界面简洁美观
- 功能完整可用
- 所有按钮点击都要有反馈
- 响应式设计，适配不同屏幕`
  };
}

/**
 * 处理来自content script的消息
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('📨 后台收到消息:', message.type);
  
  switch (message.type) {
    case 'STATUS_UPDATE':
      handleStatusUpdate(message.data, sender);
      break;
      
    case 'BEHAVIOR_DETECTED':
      handleBehaviorDetected(message.data, sender);
      break;
      
    case 'CODE_BLOCK_FOUND':
      handleCodeBlockFound(message.data, sender);
      break;
      
    case 'RUN_BUTTON_CLICKED':
      handleRunButtonClicked(message.data, sender);
      break;
      
    case 'RENDER_COMPLETED':
      handleRenderCompleted(message.data, sender);
      break;
      
    case 'PROMPT_INJECTED':
      handlePromptInjected(message.data, sender);
      break;
      
    case 'ERROR_OCCURRED':
      handleError(message.data, sender);
      break;
      
    case 'GET_SETTINGS':
      sendResponse(pluginState.userSettings);
      break;
      
    case 'UPDATE_SETTINGS':
      updateSettings(message.data);
      sendResponse({ success: true });
      break;
      
    case 'GET_PROMPT_TEMPLATES':
      getPromptTemplates().then(templates => {
        sendResponse(templates);
      });
      return true; // 保持消息通道开放
      
    default:
      console.warn('⚠️ 未知消息类型:', message.type);
  }
  
  return true; // 保持消息通道开放
});

/**
 * 处理状态更新
 */
function handleStatusUpdate(data, sender) {
  pluginState.currentTab = sender.tab;
  
  if (data.isSupported) {
    pluginState.supportedSites.add(data.site);
  }
  
  // 更新插件图标状态
  updatePluginIcon(sender.tab.id, data.isSupported);
  
  console.log('📊 状态已更新:', data);
}

/**
 * 处理用户行为检测
 */
function handleBehaviorDetected(data, sender) {
  console.log('👤 检测到用户行为:', data.description);
  
  // 可以在这里添加行为分析逻辑
  // 比如统计用户最常用的操作类型
  
  // 存储行为数据（可选）
  storeBehaviorData(data);
}

/**
 * 处理代码块发现
 */
function handleCodeBlockFound(data, sender) {
  console.log('📝 发现代码块');
  
  // 如果启用了自动运行，可以在这里处理
  if (pluginState.userSettings.autoRun) {
    // 发送自动运行命令
    chrome.tabs.sendMessage(sender.tab.id, {
      type: 'AUTO_RUN_CODE',
      data: data
    });
  }
}

/**
 * 处理运行按钮点击
 */
function handleRunButtonClicked(data, sender) {
  console.log('🏃 代码运行按钮被点击');
  
  // 启用行为追踪
  if (pluginState.userSettings.autoInject) {
    chrome.tabs.sendMessage(sender.tab.id, {
      type: 'START_MONITORING'
    });
  }
}

/**
 * 处理渲染完成
 */
function handleRenderCompleted(data, sender) {
  console.log('🎨 页面渲染完成');
  
  // 自动启用行为追踪
  chrome.tabs.sendMessage(sender.tab.id, {
    type: 'START_MONITORING'
  });
}

/**
 * 处理提示词注入
 */
function handlePromptInjected(data, sender) {
  console.log('💉 提示词已注入');
  
  // 可以在这里记录注入历史
}

/**
 * 处理错误
 */
function handleError(data, sender) {
  console.error('❌ 插件错误:', data);
  
  // 可以在这里添加错误报告逻辑
}

/**
 * 更新插件图标
 */
function updatePluginIcon(tabId, isSupported) {
  const iconPath = isSupported ? 
    'assets/icons/icon32.png' : 
    'assets/icons/icon32-disabled.png';
    
  chrome.action.setIcon({
    tabId: tabId,
    path: iconPath
  });
  
  const title = isSupported ? 
    '预见UI - 当前网站已支持' : 
    '预见UI - 当前网站不支持';
    
  chrome.action.setTitle({
    tabId: tabId,
    title: title
  });
}

/**
 * 更新设置
 */
async function updateSettings(newSettings) {
  try {
    pluginState.userSettings = { ...pluginState.userSettings, ...newSettings };
    
    await chrome.storage.local.set({
      userSettings: pluginState.userSettings
    });
    
    console.log('⚙️ 设置已更新:', newSettings);
  } catch (error) {
    console.error('❌ 设置更新失败:', error);
  }
}

/**
 * 获取提示词模板
 */
async function getPromptTemplates() {
  try {
    const result = await chrome.storage.local.get(['promptTemplates']);
    return result.promptTemplates || getDefaultPromptTemplates();
  } catch (error) {
    console.error('❌ 获取提示词模板失败:', error);
    return getDefaultPromptTemplates();
  }
}

/**
 * 存储行为数据
 */
async function storeBehaviorData(behaviorData) {
  try {
    // 获取现有数据
    const result = await chrome.storage.local.get(['behaviorHistory']);
    const history = result.behaviorHistory || [];
    
    // 添加新数据
    history.push({
      ...behaviorData,
      timestamp: Date.now()
    });
    
    // 限制历史记录长度
    if (history.length > 1000) {
      history.splice(0, history.length - 1000);
    }
    
    // 保存数据
    await chrome.storage.local.set({
      behaviorHistory: history
    });
  } catch (error) {
    console.error('❌ 存储行为数据失败:', error);
  }
}

/**
 * 标签页更新监听
 */
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 检查是否为支持的网站
    const supportedDomains = [
      'chat.deepseek.com',
      'kimi.moonshot.cn',
      'chatgpt.com',
      'claude.ai',
      'gemini.google.com',
      'tongyi.aliyun.com'
    ];
    
    const isSupported = supportedDomains.some(domain => tab.url.includes(domain));
    updatePluginIcon(tabId, isSupported);
  }
});

/**
 * 标签页激活监听
 */
chrome.tabs.onActivated.addListener((activeInfo) => {
  chrome.tabs.get(activeInfo.tabId, (tab) => {
    if (tab.url) {
      const supportedDomains = [
        'chat.deepseek.com',
        'kimi.moonshot.cn',
        'chatgpt.com',
        'claude.ai',
        'gemini.google.com',
        'tongyi.aliyun.com'
      ];
      
      const isSupported = supportedDomains.some(domain => tab.url.includes(domain));
      updatePluginIcon(activeInfo.tabId, isSupported);
    }
  });
});

console.log('🔧 后台服务脚本已加载');
