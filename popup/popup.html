<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预见UI - LLM交互增强插件</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="logo">
                <img src="../assets/icons/icon32.png" alt="预见UI" class="logo-icon">
                <h1 class="title">预见UI</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">检测中...</span>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 网站状态卡片 -->
            <div class="card site-status-card">
                <h3 class="card-title">当前网站状态</h3>
                <div class="site-info">
                    <div class="site-name" id="siteName">未检测到支持的网站</div>
                    <div class="site-url" id="siteUrl">-</div>
                </div>
                <div class="status-badges">
                    <span class="badge" id="supportBadge">不支持</span>
                    <span class="badge" id="newChatBadge">非新对话</span>
                </div>
            </div>

            <!-- 快速启动区域 -->
            <div class="card quick-start-card" id="quickStartCard" style="display: block;">
                <h3 class="card-title">🚀 启动预见UI</h3>
                <div class="task-input-section">
                    <label for="userTask" class="input-label">请描述您想要创建的应用：</label>
                    <textarea 
                        id="userTask" 
                        class="task-input" 
                        placeholder="例如：创建一个MBTI性格测试页面"
                        rows="3"
                    ></textarea>
                </div>
                
                <div class="template-section">
                    <label for="promptTemplate" class="input-label">选择提示词模板：</label>
                    <select id="promptTemplate" class="template-select">
                        <option value="default">默认模板</option>
                        <option value="interactive">交互式模板</option>
                        <option value="simple">简单模板</option>
                    </select>
                </div>

                <button id="startForeseeUI" class="primary-button">
                    <span class="button-icon">🎯</span>
                    启动预见UI
                </button>
            </div>

            <!-- 功能控制区域 -->
            <div class="card controls-card">
                <h3 class="card-title">功能控制</h3>
                
                <div class="control-group">
                    <div class="control-item">
                        <label class="switch">
                            <input type="checkbox" id="monitoringToggle" checked>
                            <span class="slider"></span>
                        </label>
                        <div class="control-info">
                            <div class="control-name">用户行为监控</div>
                            <div class="control-desc">监控用户在页面上的操作行为</div>
                        </div>
                    </div>

                    <div class="control-item">
                        <label class="switch">
                            <input type="checkbox" id="autoRunToggle" checked>
                            <span class="slider"></span>
                        </label>
                        <div class="control-info">
                            <div class="control-name">自动运行代码</div>
                            <div class="control-desc">自动点击代码块的运行按钮</div>
                        </div>
                    </div>

                    <div class="control-item">
                        <label class="switch">
                            <input type="checkbox" id="autoInjectToggle" checked>
                            <span class="slider"></span>
                        </label>
                        <div class="control-info">
                            <div class="control-name">自动注入行为</div>
                            <div class="control-desc">将用户操作追加到输入框</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态信息区域 -->
            <div class="card status-card">
                <h3 class="card-title">运行状态</h3>
                <div class="status-info">
                    <div class="status-item">
                        <span class="status-label">监控状态：</span>
                        <span class="status-value" id="monitoringStatus">未启动</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">检测到的代码块：</span>
                        <span class="status-value" id="codeBlockCount">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">用户行为记录：</span>
                        <span class="status-value" id="behaviorCount">0</span>
                    </div>
                </div>
            </div>

            <!-- 帮助信息 -->
            <div class="card help-card">
                <h3 class="card-title">使用说明</h3>
                <div class="help-content">
                    <ol class="help-list">
                        <li>在支持的LLM网站上打开新对话</li>
                        <li>点击"启动预见UI"开始创建应用</li>
                        <li>等待LLM生成代码并自动运行</li>
                        <li>在渲染的页面上进行操作</li>
                        <li>插件会自动将您的操作反馈给LLM</li>
                    </ol>
                </div>
            </div>
        </main>

        <!-- 底部 -->
        <footer class="footer">
            <div class="footer-links">
                <a href="#" id="settingsLink" class="footer-link">设置</a>
                <a href="#" id="helpLink" class="footer-link">帮助</a>
                <a href="#" id="aboutLink" class="footer-link">关于</a>
            </div>
            <div class="version">v1.0.0</div>
        </footer>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在启动预见UI...</div>
    </div>

    <!-- 通知容器 -->
    <div class="notification-container" id="notificationContainer"></div>

    <script src="popup.js"></script>
</body>
</html>
