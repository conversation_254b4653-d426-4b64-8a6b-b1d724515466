<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户行为流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .deepseek-button {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .input-area {
            width: 100%;
            min-height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: inherit;
            resize: vertical;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 用户行为流程测试</h1>
        <p>这个页面用于测试完整的用户行为追踪和反馈流程。</p>
        
        <div id="status" class="status error">
            ❌ 等待扩展加载...
        </div>
    </div>

    <div class="container">
        <h2>🎮 测试操作</h2>
        <button class="test-button" onclick="testClick('普通按钮')">普通按钮</button>
        <button class="deepseek-button _7db3914" onclick="testClick('DeepSeek运行按钮')">运行代码</button>
        <button class="test-button _6f28693" onclick="testClick('DeepSeek发送按钮')">发送</button>
        
        <br><br>
        
        <input type="text" placeholder="输入测试文本..." onchange="testInput(this.value)" style="width: 300px; padding: 8px;">
        <textarea placeholder="多行文本测试..." onchange="testInput(this.value)" rows="3" style="width: 300px; padding: 8px; margin-left: 10px;"></textarea>
    </div>

    <div class="container">
        <h2>📋 行为日志</h2>
        <div id="behaviorLog" class="log-area">
            等待用户操作...
        </div>
        <button onclick="clearLog()" style="background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px;">清空日志</button>
    </div>

    <div class="container">
        <h2>💬 模拟输入框</h2>
        <p>用户行为反馈会自动追加到这里：</p>
        <textarea id="mockInput" class="input-area" placeholder="用户行为反馈会自动追加到这里..."></textarea>
        <br>
        <button onclick="sendFeedback()" style="background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin-top: 10px;">📤 发送反馈</button>
        <button onclick="clearInput()" style="background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin-top: 10px; margin-left: 10px;">🗑️ 清空输入框</button>
    </div>

    <script>
        // 日志记录函数
        function log(message) {
            const logElement = document.getElementById('behaviorLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 更新状态
        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // 测试点击
        function testClick(buttonName) {
            log(`🖱️ 用户点击了: ${buttonName}`);
            
            // 模拟生成反馈消息
            const feedback = generateTestFeedback('click', `用户点击了${buttonName}`, buttonName);
            appendToInput(feedback);
        }

        // 测试输入
        function testInput(value) {
            if (value.trim()) {
                log(`⌨️ 用户输入了: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
                
                // 模拟生成反馈消息
                const feedback = generateTestFeedback('input', `用户输入了文本`, null, value);
                appendToInput(feedback);
            }
        }

        // 生成测试反馈消息
        function generateTestFeedback(type, description, elementText, value) {
            const timeInfo = {
                time: new Date().toLocaleTimeString('zh-CN', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                })
            };

            let message = `[用户操作反馈]\n`;
            message += `操作: ${description}\n`;
            message += `时间: ${timeInfo.time}\n`;
            
            if (elementText) {
                message += `元素: 按钮 "${elementText}"\n`;
            }
            
            if (value) {
                const valueType = getValueType(value);
                message += `输入内容: ${value} (${valueType})\n`;
            }
            
            message += `页面: 用户行为流程测试\n`;
            message += `位置: 测试页面\n`;
            message += `[/用户操作反馈]`;

            return message;
        }

        // 获取输入值类型
        function getValueType(value) {
            if (!value) return '空内容';
            if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return '邮箱';
            if (/^[\d\s\-\+\(\)]+$/.test(value) && value.replace(/\D/g, '').length >= 10) return '电话';
            if (/^https?:\/\//.test(value)) return '网址';
            if (/^\d+$/.test(value)) return '数字';
            if (/[\u4e00-\u9fa5]/.test(value)) return '中文';
            if (value.length > 100) return '长文本';
            return '文本';
        }

        // 追加到输入框
        function appendToInput(feedback) {
            const inputElement = document.getElementById('mockInput');
            const currentValue = inputElement.value;
            
            if (currentValue.trim() === '') {
                inputElement.value = feedback;
            } else {
                inputElement.value = currentValue + '\n\n' + feedback;
            }
            
            // 滚动到底部
            inputElement.scrollTop = inputElement.scrollHeight;
            
            log(`✅ 反馈已追加到输入框`);
        }

        // 发送反馈
        function sendFeedback() {
            const inputElement = document.getElementById('mockInput');
            const message = inputElement.value.trim();
            
            if (message) {
                log(`📤 发送反馈消息 (${message.length} 字符)`);
                inputElement.value = '';
            } else {
                log(`⚠️ 输入框为空，无法发送`);
            }
        }

        // 清空输入框
        function clearInput() {
            document.getElementById('mockInput').value = '';
            log(`🗑️ 输入框已清空`);
        }

        // 清空日志
        function clearLog() {
            document.getElementById('behaviorLog').textContent = '日志已清空...\n';
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            log('📄 测试页面加载完成');
            
            setTimeout(() => {
                if (window.BehaviorTracker) {
                    updateStatus('✅ 扩展已加载，可以进行测试', 'success');
                    log('✅ 检测到行为追踪器，扩展功能正常');
                } else {
                    updateStatus('⚠️ 扩展未加载，使用模拟模式', 'error');
                    log('⚠️ 未检测到扩展，使用模拟模式进行测试');
                }
            }, 1000);
        });
    </script>
</body>
</html>
