<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行为追踪器测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .deepseek-style {
            background: #1a1a1a;
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        
        ._6f28693 {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        ._7db3914 {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        ._5d271a3 {
            background: #dc3545;
            color: white;
            padding: 12px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            margin: 5px;
            width: 40px;
            height: 40px;
        }
        
        .ds-button {
            background: #6c757d;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .ds-icon-button {
            background: #17a2b8;
            color: white;
            padding: 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .iframe-container {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 行为追踪器测试页面</h1>
        <p>这个页面用于测试用户行为追踪功能，包含各种DeepSeek样式的元素。</p>
        
        <div id="status" class="status error">
            ❌ 行为追踪器未初始化
        </div>
    </div>

    <div class="container">
        <h2>📝 输入测试</h2>
        <input type="text" placeholder="在这里输入文本测试..." id="testInput">
        <textarea placeholder="在这里输入多行文本测试..." rows="4" id="testTextarea"></textarea>
        <div contenteditable="true" style="border: 1px solid #ddd; padding: 10px; min-height: 50px;">
            可编辑的div元素，点击这里输入内容...
        </div>
    </div>

    <div class="container">
        <h2>🔘 DeepSeek样式按钮测试</h2>
        <div class="deepseek-style">
            <button class="_6f28693">DeepSeek发送按钮</button>
            <button class="_7db3914">DeepSeek运行按钮</button>
            <button class="_5d271a3">×</button>
            <button class="ds-button">DS按钮</button>
            <button class="ds-icon-button">🔧</button>
            <div role="button" class="ds-button" style="display: inline-block;">Role按钮</div>
        </div>
    </div>

    <div class="container">
        <h2>🖼️ iframe测试</h2>
        <div class="iframe-container">
            <iframe id="testIframe" src="data:text/html,<html><body><h3>iframe内容</h3><button onclick='parent.postMessage({type:\"user_action\", actionType:\"click\", description:\"iframe内按钮点击\"}, \"*\")'>iframe内按钮</button><input placeholder='iframe内输入框'></body></html>" 
                    width="100%" height="150" style="border: none;"></iframe>
        </div>
    </div>

    <div class="container">
        <h2>📊 控制面板</h2>
        <button onclick="startTracking()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 5px;">
            ▶️ 开始追踪
        </button>
        <button onclick="stopTracking()" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 5px;">
            ⏹️ 停止追踪
        </button>
        <button onclick="clearLog()" style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 5px;">
            🗑️ 清空日志
        </button>
    </div>

    <div class="container">
        <h2>📋 行为日志</h2>
        <div id="behaviorLog" class="log">
            等待行为追踪器初始化...
        </div>
    </div>

    <script>
        // 模拟消息类型常量
        window.MESSAGE_TYPES = {
            BEHAVIOR_DETECTED: 'BEHAVIOR_DETECTED',
            START_MONITORING: 'START_MONITORING',
            STOP_MONITORING: 'STOP_MONITORING',
            RENDER_COMPLETED: 'RENDER_COMPLETED'
        };

        // 模拟消息桥接器
        class MockMessageBridge {
            constructor() {
                this.listeners = new Map();
            }

            addListener(type, handler) {
                if (!this.listeners.has(type)) {
                    this.listeners.set(type, []);
                }
                this.listeners.get(type).push(handler);
                console.log('📡 添加监听器:', type);
            }

            sendMessage(type, data) {
                console.log('📤 发送消息:', type, data);
                
                // 记录到页面日志
                if (type === MESSAGE_TYPES.BEHAVIOR_DETECTED) {
                    logBehavior(data);
                }

                // 调用监听器
                if (this.listeners.has(type)) {
                    this.listeners.get(type).forEach(handler => {
                        try {
                            handler(data);
                        } catch (error) {
                            console.error('❌ 监听器执行错误:', error);
                        }
                    });
                }
            }
        }

        // 创建模拟的消息桥接器
        window.MessageBridge = new MockMessageBridge();

        // 日志记录函数
        function logBehavior(data) {
            const logElement = document.getElementById('behaviorLog');
            const timestamp = new Date().toLocaleTimeString();

            // 显示详细的行为信息
            let logEntry = `[${timestamp}] ${data.description}`;

            if (data.type) {
                logEntry += ` (类型: ${data.type})`;
            }

            if (data.target && data.target.className) {
                logEntry += ` (元素: ${data.target.className})`;
            }

            if (data.coordinates) {
                logEntry += ` (坐标: ${data.coordinates.x}, ${data.coordinates.y})`;
            }

            logEntry += '\n';

            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 控制函数
        function startTracking() {
            if (window.BehaviorTracker) {
                window.BehaviorTracker.startTracking();
                updateStatus('✅ 行为追踪已启动', 'success');
            } else {
                updateStatus('❌ 行为追踪器未加载', 'error');
            }
        }

        function stopTracking() {
            if (window.BehaviorTracker) {
                window.BehaviorTracker.stopTracking();
                updateStatus('⏹️ 行为追踪已停止', 'error');
            } else {
                updateStatus('❌ 行为追踪器未加载', 'error');
            }
        }

        function clearLog() {
            document.getElementById('behaviorLog').textContent = '';
        }

        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('📄 测试页面加载完成');
            
            // 检查行为追踪器是否已加载
            setTimeout(() => {
                if (window.BehaviorTracker) {
                    updateStatus('✅ 行为追踪器已加载', 'success');
                    logBehavior({description: '行为追踪器初始化完成'});
                } else {
                    updateStatus('❌ 行为追踪器未加载，请确保扩展已安装', 'error');
                }
            }, 1000);
        });

        // 监听来自iframe的消息
        window.addEventListener('message', (event) => {
            if (event.data.type === 'user_action') {
                console.log('🖼️ 收到iframe消息:', event.data);
                logBehavior({description: `iframe消息: ${event.data.description}`});
            }
        });
    </script>
</body>
</html>
