/**
 * 代码监控器 - 监控代码块的运行和渲染
 * 负责检测代码块运行按钮，并在需要时自动点击运行
 * 优化版本：减少日志输出，提高性能
 */

class CodeMonitor {
  constructor() {
    this.isAutoRunEnabled = false;
    this.monitoredCodeBlocks = new Set();
    this.runButtonObserver = null;
    this.autoRunDelay = 1000; // 自动运行延迟
    
    this.init();
  }
  
  /**
   * 初始化代码监控器 - 优化版本，减少日志输出
   */
  init() {
    // console.log('🔍 代码监控器初始化中...'); // 禁用日志

    // 监听代码块发现事件
    if (window.MessageBridge) {
      window.MessageBridge.addListener(window.MESSAGE_TYPES.CODE_BLOCK_FOUND, (data) => {
        this.handleCodeBlockFound(data);
      });

      // 监听运行按钮点击事件
      window.MessageBridge.addListener(window.MESSAGE_TYPES.RUN_BUTTON_CLICKED, (data) => {
        this.handleRunButtonClicked(data);
      });
    }

    // 设置运行按钮观察器
    this.setupRunButtonObserver();

    // console.log('✅ 代码监控器初始化完成'); // 禁用日志
  }
  
  /**
   * 处理发现的代码块 - 优化版本，减少日志输出
   * @param {Object} data 代码块数据
   */
  handleCodeBlockFound(data) {
    const { codeBlock, runButton } = data;

    // console.log('📝 发现新的代码块:', codeBlock); // 禁用日志

    // 检查是否已经监控过
    const blockId = this.generateBlockId(codeBlock);
    if (this.monitoredCodeBlocks.has(blockId)) {
      // console.log('ℹ️ 代码块已在监控中'); // 禁用日志
      return;
    }

    // 添加到监控列表
    this.monitoredCodeBlocks.add(blockId);

    // 如果启用了自动运行，则延迟执行
    if (this.isAutoRunEnabled && runButton) {
      // console.log('🤖 准备自动运行代码块'); // 禁用日志
      setTimeout(() => {
        this.autoRunCodeBlock(runButton);
      }, this.autoRunDelay);
    }
  }
  
  /**
   * 处理运行按钮点击事件 - 优化版本，减少日志输出
   * @param {Object} data 运行按钮数据
   */
  handleRunButtonClicked(data) {
    const { codeBlock, runButton } = data;

    // console.log('🏃 代码块运行按钮被点击'); // 禁用日志

    // 开始监控渲染结果
    this.startRenderMonitoring();

    // 启用行为追踪（如果还没启用）
    if (window.AutoInjector) {
      window.AutoInjector.enableAutoMode();
    }
  }
  
  /**
   * 自动运行代码块 - 优化版本，减少日志输出
   * @param {Object} runButtonInfo 运行按钮信息
   */
  autoRunCodeBlock(runButtonInfo) {
    // 根据按钮信息找到实际的DOM元素
    const runButton = this.findRunButtonElement(runButtonInfo);

    if (!runButton) {
      // console.warn('⚠️ 无法找到运行按钮元素'); // 禁用日志
      return;
    }

    // 检查按钮是否可用
    if (runButton.disabled || runButton.style.display === 'none') {
      // console.warn('⚠️ 运行按钮当前不可用'); // 禁用日志
      return;
    }

    // console.log('🚀 自动点击运行按钮'); // 禁用日志

    // 模拟点击
    this.simulateClick(runButton);

    // 发送自动运行事件
    if (window.MessageBridge) {
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.RUN_BUTTON_CLICKED, {
        auto: true,
        runButton: runButtonInfo
      });
    }
  }
  
  /**
   * 根据按钮信息找到DOM元素
   * @param {Object} buttonInfo 按钮信息
   * @returns {Element|null} 找到的按钮元素
   */
  findRunButtonElement(buttonInfo) {
    // 尝试通过ID查找
    if (buttonInfo.id) {
      const element = document.getElementById(buttonInfo.id);
      if (element) return element;
    }
    
    // 尝试通过类名查找
    if (buttonInfo.className) {
      const elements = document.getElementsByClassName(buttonInfo.className);
      for (let element of elements) {
        if (this.isMatchingButton(element, buttonInfo)) {
          return element;
        }
      }
    }
    
    // 通过文本内容查找
    const buttons = document.querySelectorAll('button');
    for (let button of buttons) {
      if (this.isMatchingButton(button, buttonInfo)) {
        return button;
      }
    }
    
    return null;
  }
  
  /**
   * 检查按钮是否匹配
   * @param {Element} button 按钮元素
   * @param {Object} buttonInfo 按钮信息
   * @returns {boolean} 是否匹配
   */
  isMatchingButton(button, buttonInfo) {
    // 检查文本内容
    const buttonText = button.textContent.trim();
    const infoText = buttonInfo.textContent || '';
    
    if (buttonText && infoText && buttonText.includes(infoText.substring(0, 10))) {
      return true;
    }
    
    // 检查运行相关的关键词
    const runKeywords = ['运行', 'Run', '执行', 'Execute', 'Play'];
    return runKeywords.some(keyword => buttonText.includes(keyword));
  }
  
  /**
   * 模拟点击事件
   * @param {Element} element 要点击的元素
   */
  simulateClick(element) {
    // 创建鼠标事件
    const mouseEvents = ['mousedown', 'mouseup', 'click'];
    
    mouseEvents.forEach(eventType => {
      const event = new MouseEvent(eventType, {
        bubbles: true,
        cancelable: true,
        view: window
      });
      element.dispatchEvent(event);
    });
    
    // 如果是按钮，也触发focus事件
    if (element.tagName === 'BUTTON') {
      element.focus();
    }
  }
  
  /**
   * 开始监控渲染结果 - 优化版本，减少日志输出
   */
  startRenderMonitoring() {
    // console.log('👀 开始监控渲染结果'); // 禁用日志

    // 设置延迟检查，因为渲染可能需要时间
    const checkIntervals = [500, 1000, 2000, 3000];

    checkIntervals.forEach(delay => {
      setTimeout(() => {
        this.checkForNewRenderResults();
      }, delay);
    });
  }

  /**
   * 检查新的渲染结果 - 优化版本，减少日志输出
   */
  checkForNewRenderResults() {
    const config = window.SiteConfigs?.getCurrentSiteConfig();
    if (!config) return;

    // 查找渲染容器
    const renderContainers = document.querySelectorAll(config.renderContainerSelector);

    renderContainers.forEach(container => {
      if (!container.dataset.foreseeTracked) {
        // console.log('🎨 发现新的渲染结果'); // 禁用日志

        // 标记为已追踪
        container.dataset.foreseeTracked = 'true';

        // 发送渲染完成事件
        if (window.MessageBridge) {
          window.MessageBridge.sendMessage(window.MESSAGE_TYPES.RENDER_COMPLETED, {
            container: this.getElementInfo(container)
          });
        }
      }
    });
  }
  
  /**
   * 设置运行按钮观察器 - 优化版本，减少性能开销
   * 监控新出现的运行按钮
   */
  setupRunButtonObserver() {
    // 使用节流的观察器，减少触发频率
    let observerTimeout = null;

    this.runButtonObserver = new MutationObserver((mutations) => {
      // 清除之前的定时器
      if (observerTimeout) {
        clearTimeout(observerTimeout);
      }

      // 节流处理，1秒内只处理一次
      observerTimeout = setTimeout(() => {
        mutations.forEach(mutation => {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.scanForRunButtons(node);
            }
          });
        });
      }, 1000);
    });

    // 开始观察，减少观察范围
    this.runButtonObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    });

    // console.log('👀 运行按钮观察器已启动'); // 禁用日志
  }
  
  /**
   * 扫描运行按钮 - 优化版本，减少日志输出
   * @param {Element} element 要扫描的元素
   */
  scanForRunButtons(element) {
    const config = window.SiteConfigs?.getCurrentSiteConfig();
    if (!config) return;

    // 查找运行按钮，限制数量避免性能问题
    const runButtons = Array.from(element.querySelectorAll('button')).slice(0, 20);

    runButtons.forEach(button => {
      const buttonText = button.textContent.trim();
      const runKeywords = ['运行', 'Run', '执行', 'Execute', 'Play'];

      if (runKeywords.some(keyword => buttonText.includes(keyword))) {
        // 检查是否已经处理过
        if (button.dataset.foreseeProcessed) return;

        button.dataset.foreseeProcessed = 'true';

        // console.log('🔍 发现新的运行按钮:', buttonText); // 禁用日志

        // 查找关联的代码块
        const codeBlock = this.findAssociatedCodeBlock(button);

        if (codeBlock) {
          // 发送代码块发现事件
          if (window.MessageBridge) {
            window.MessageBridge.sendMessage(window.MESSAGE_TYPES.CODE_BLOCK_FOUND, {
              codeBlock: this.getElementInfo(codeBlock),
              runButton: this.getElementInfo(button)
            });
          }
        }
      }
    });
  }
  
  /**
   * 查找关联的代码块
   * @param {Element} runButton 运行按钮
   * @returns {Element|null} 关联的代码块
   */
  findAssociatedCodeBlock(runButton) {
    const config = window.SiteConfigs.getCurrentSiteConfig();
    if (!config) return null;
    
    // 在按钮的父容器中查找代码块
    let container = runButton.closest('.message, .chat-message, .response') || runButton.parentElement;
    
    for (let i = 0; i < 3 && container; i++) {
      const codeBlock = container.querySelector(config.codeBlockSelector);
      if (codeBlock) return codeBlock;
      container = container.parentElement;
    }
    
    return null;
  }
  
  /**
   * 生成代码块ID
   * @param {Object} codeBlockInfo 代码块信息
   * @returns {string} 代码块ID
   */
  generateBlockId(codeBlockInfo) {
    return `${codeBlockInfo.tagName}_${codeBlockInfo.className}_${codeBlockInfo.textContent.substring(0, 50)}`;
  }
  
  /**
   * 获取元素信息
   * @param {Element} element 元素
   * @returns {Object} 元素信息
   */
  getElementInfo(element) {
    return {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      textContent: element.textContent.substring(0, 100)
    };
  }
  
  /**
   * 启用自动运行 - 优化版本，减少日志输出
   */
  enableAutoRun() {
    this.isAutoRunEnabled = true;
    // console.log('🤖 自动运行已启用'); // 禁用日志

    if (window.MessageBridge) {
      window.MessageBridge.sendStatusUpdate({
        autoRun: true,
        message: '代码块自动运行已启用'
      });
    }
  }

  /**
   * 禁用自动运行 - 优化版本，减少日志输出
   */
  disableAutoRun() {
    this.isAutoRunEnabled = false;
    // console.log('🔒 自动运行已禁用'); // 禁用日志

    if (window.MessageBridge) {
      window.MessageBridge.sendStatusUpdate({
        autoRun: false,
        message: '代码块自动运行已禁用'
      });
    }
  }
  
  /**
   * 切换自动运行状态
   */
  toggleAutoRun() {
    if (this.isAutoRunEnabled) {
      this.disableAutoRun();
    } else {
      this.enableAutoRun();
    }
  }
  
  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getStatus() {
    return {
      isAutoRunEnabled: this.isAutoRunEnabled,
      monitoredBlocksCount: this.monitoredCodeBlocks.size,
      autoRunDelay: this.autoRunDelay
    };
  }
  
  /**
   * 销毁监控器 - 优化版本，减少日志输出
   */
  destroy() {
    if (this.runButtonObserver) {
      this.runButtonObserver.disconnect();
    }
    // console.log('🗑️ 代码监控器已销毁'); // 禁用日志
  }
}

// 创建代码监控器实例
const codeMonitor = new CodeMonitor();

// 导出监控器
window.CodeMonitor = codeMonitor;

// console.log('🔍 代码监控器模块已加载'); // 禁用日志
