/**
 * 用户行为追踪器 - 监控用户在渲染页面上的交互行为
 * 将用户操作转换为自然语言描述，用于LLM理解用户意图
 */

class BehaviorTracker {
  constructor() {
    this.isTracking = false;
    this.trackedContainers = new Set();
    this.actionQueue = [];
    this.lastActionTime = 0;
    this.actionThrottleDelay = 500; // 防抖延迟
    this.contentObserver = null; // 内容观察器

    this.init();
  }
  
  /**
   * 初始化行为追踪器 - 优化版本，减少不必要的监听
   */
  init() {
    console.log('👁️ 用户行为追踪器初始化中...'); // 启用日志用于调试

    // 只在需要时监听事件
    if (window.MessageBridge) {
      console.log('📡 MessageBridge存在，设置监听器');

      // 监听渲染完成事件
      window.MessageBridge.addListener(window.MESSAGE_TYPES.RENDER_COMPLETED, (data) => {
        console.log('🎨 收到渲染完成事件:', data);
        this.startTrackingContainer(data.container);
      });

      // 监听开始/停止追踪命令
      window.MessageBridge.addListener(window.MESSAGE_TYPES.START_MONITORING, () => {
        console.log('▶️ 收到开始监控命令');
        this.startTracking();
      });

      window.MessageBridge.addListener(window.MESSAGE_TYPES.STOP_MONITORING, () => {
        console.log('⏹️ 收到停止监控命令');
        this.stopTracking();
      });
    } else {
      console.warn('⚠️ MessageBridge不存在，无法设置监听器');
    }

    console.log('✅ 用户行为追踪器初始化完成'); // 启用日志用于调试
  }
  
  /**
   * 开始追踪指定容器 - 优化版本
   * @param {Object} containerInfo 容器信息
   */
  startTrackingContainer(containerInfo) {
    // 根据容器信息找到实际的DOM元素
    const container = this.findContainerElement(containerInfo);
    if (!container) {
      // console.warn('⚠️ 无法找到渲染容器元素'); // 禁用日志
      return;
    }

    if (this.trackedContainers.has(container)) {
      // console.log('ℹ️ 容器已在追踪中'); // 禁用日志
      return;
    }

    // console.log('🎯 开始追踪容器:', containerInfo); // 禁用日志
    this.trackedContainers.add(container);
    this.setupLightweightListeners(container); // 使用轻量级监听器
  }
  
  /**
   * 根据容器信息找到DOM元素
   * @param {Object} containerInfo 容器信息
   * @returns {Element|null} 找到的元素
   */
  findContainerElement(containerInfo) {
    // 尝试通过ID查找
    if (containerInfo.id) {
      const element = document.getElementById(containerInfo.id);
      if (element) return element;
    }
    
    // 尝试通过类名查找
    if (containerInfo.className) {
      const elements = document.getElementsByClassName(containerInfo.className);
      if (elements.length > 0) return elements[0];
    }
    
    // 尝试通过标签名和文本内容查找
    const elements = document.querySelectorAll(containerInfo.tagName);
    for (let element of elements) {
      if (element.textContent.includes(containerInfo.textContent.substring(0, 50))) {
        return element;
      }
    }
    
    return null;
  }
  
  /**
   * 为容器设置轻量级事件监听器 - 大幅减少性能开销
   * @param {Element} container 容器元素
   */
  setupLightweightListeners(container) {
    // 只监听关键的点击事件，使用节流
    let clickTimeout = null;
    container.addEventListener('click', (event) => {
      if (clickTimeout) return; // 防止频繁触发

      clickTimeout = setTimeout(() => {
        this.handleClickAction(event);
        clickTimeout = null;
      }, 200);
    }, { passive: true, capture: true });

    // 只监听重要的输入事件，大幅增加节流时间
    let inputTimeout = null;
    container.addEventListener('input', (event) => {
      if (inputTimeout) clearTimeout(inputTimeout);

      inputTimeout = setTimeout(() => {
        this.handleInputAction(event);
      }, 1000); // 增加到1秒
    }, { passive: true });

    // 移除悬停和滚动监听，这些会造成大量性能开销
    // 只保留表单提交监听
    container.addEventListener('submit', (event) => {
      this.handleSubmitAction(event);
    }, { passive: true });

    // console.log('👂 轻量级容器监听器已设置'); // 禁用日志
  }

  /**
   * 为容器设置事件监听器 - 保留原方法用于兼容性
   * @param {Element} container 容器元素
   */
  setupContainerListeners(container) {
    // 使用轻量级监听器
    this.setupLightweightListeners(container);
  }
  
  /**
   * 处理点击行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleClickAction(event) {
    const target = event.target;

    // 只处理重要的点击事件
    if (!this.isImportantElement(target)) {
      return;
    }

    const actionDescription = this.generateClickDescription(target);

    this.recordAction({
      type: 'click',
      target: this.getElementDescription(target),
      description: actionDescription,
      timestamp: Date.now(),
      coordinates: { x: event.clientX, y: event.clientY }
    });
  }

  /**
   * 处理iframe内的点击行为
   * @param {Event} event 事件对象
   * @param {Element} iframe iframe元素
   */
  handleIframeClickAction(event, iframe) {
    const target = event.target;

    // 只处理重要的点击事件
    if (!this.isImportantElement(target)) {
      return;
    }

    const actionDescription = this.generateClickDescription(target);

    this.recordAction({
      type: 'click',
      target: this.getElementDescription(target),
      description: `[iframe内] ${actionDescription}`,
      timestamp: Date.now(),
      coordinates: { x: event.clientX, y: event.clientY },
      iframe: {
        src: iframe.src,
        id: iframe.id,
        className: iframe.className
      }
    });
  }

  /**
   * 处理iframe内的输入行为
   * @param {Event} event 事件对象
   * @param {Element} iframe iframe元素
   */
  handleIframeInputAction(event, iframe) {
    const target = event.target;
    const value = target.value;

    // 只处理有意义的输入
    if (!value || value.length < 2) {
      return;
    }

    const actionDescription = this.generateInputDescription(target, value);

    this.recordAction({
      type: 'input',
      target: this.getElementDescription(target),
      description: `[iframe内] ${actionDescription}`,
      value: value.substring(0, 100), // 限制长度
      timestamp: Date.now(),
      iframe: {
        src: iframe.src,
        id: iframe.id,
        className: iframe.className
      }
    });
  }

  /**
   * 处理来自iframe的消息
   * @param {Object} data 消息数据
   * @param {Element} iframe iframe元素
   */
  handleIframeMessage(data, iframe) {
    // 处理来自iframe的用户行为消息
    if (data.type === 'user_action') {
      this.recordAction({
        type: data.actionType || 'unknown',
        target: data.target || {},
        description: `[iframe内] ${data.description || '用户操作'}`,
        timestamp: Date.now(),
        iframe: {
          src: iframe.src,
          id: iframe.id,
          className: iframe.className
        }
      });
    }
  }
  
  /**
   * 处理输入行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleInputAction(event) {
    const target = event.target;
    const value = target.value;

    // 只处理有意义的输入
    if (!value || value.length < 2) {
      return;
    }

    const actionDescription = this.generateInputDescription(target, value);

    this.recordAction({
      type: 'input',
      target: this.getElementDescription(target),
      description: actionDescription,
      value: value.substring(0, 100), // 限制长度
      timestamp: Date.now()
    });
  }
  
  /**
   * 处理表单提交行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleSubmitAction(event) {
    const form = event.target;
    const formData = new FormData(form);
    const actionDescription = this.generateSubmitDescription(form, formData);

    this.recordAction({
      type: 'submit',
      target: this.getElementDescription(form),
      description: actionDescription,
      formData: Object.fromEntries(formData),
      timestamp: Date.now()
    });
  }
  
  // 悬停和滚动处理已移除，以提高性能
  
  /**
   * 生成点击行为描述 - 增强版本，特别支持DeepSeek
   * @param {Element} target 目标元素
   * @returns {string} 行为描述
   */
  generateClickDescription(target) {
    const elementType = this.getElementType(target);
    const elementText = this.getElementText(target);
    const className = target.className || '';
    const role = target.getAttribute('role');

    // DeepSeek特定元素识别
    if (className.includes('_6f28693') || className.includes('ds-icon') || className.includes('_5d271a3')) {
      return `用户点击了DeepSeek发送按钮`;
    }

    if (className.includes('_7db3914')) {
      return `用户点击了DeepSeek运行按钮"${elementText}"`;
    }

    if (className.includes('ds-button') || className.includes('ds-icon-button')) {
      return `用户点击了DeepSeek按钮"${elementText}"`;
    }

    // 基于role属性的识别
    if (role === 'button') {
      if (elementText.includes('运行') || elementText.includes('Run')) {
        return `用户点击了运行按钮"${elementText}"`;
      } else if (elementText.includes('发送') || elementText.includes('Send')) {
        return `用户点击了发送按钮"${elementText}"`;
      } else {
        return `用户点击了按钮"${elementText}"`;
      }
    }

    // 传统元素识别
    if (target.tagName === 'BUTTON') {
      return `用户点击了按钮"${elementText}"`;
    } else if (target.tagName === 'A') {
      return `用户点击了链接"${elementText}"`;
    } else if (target.tagName === 'INPUT') {
      if (target.type === 'checkbox') {
        return `用户${target.checked ? '选中' : '取消选中'}了复选框"${elementText}"`;
      } else if (target.type === 'radio') {
        return `用户选择了单选按钮"${elementText}"`;
      } else if (target.type === 'submit') {
        return `用户点击了提交按钮"${elementText}"`;
      }
    } else if (target.tagName === 'SELECT') {
      return `用户点击了下拉选择框"${elementText}"`;
    }

    // 检查是否包含SVG图标（通常是按钮）
    if (target.querySelector('svg') || target.closest('[role="button"]')) {
      return `用户点击了图标按钮"${elementText}"`;
    }

    return `用户点击了${elementType}"${elementText}"`;
  }
  
  /**
   * 生成输入行为描述
   * @param {Element} target 目标元素
   * @param {string} value 输入值
   * @returns {string} 行为描述
   */
  generateInputDescription(target, value) {
    const placeholder = target.placeholder || '';
    const label = this.findLabelForInput(target);
    
    if (target.type === 'password') {
      return `用户在密码输入框中输入了内容`;
    } else if (target.type === 'email') {
      return `用户在邮箱输入框中输入了: ${value}`;
    } else if (target.type === 'number') {
      return `用户在数字输入框中输入了: ${value}`;
    } else if (target.tagName === 'TEXTAREA') {
      return `用户在文本区域中输入了: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`;
    }
    
    const fieldName = label || placeholder || '输入框';
    return `用户在"${fieldName}"中输入了: ${value}`;
  }
  
  /**
   * 生成提交行为描述
   * @param {Element} form 表单元素
   * @param {FormData} formData 表单数据
   * @returns {string} 行为描述
   */
  generateSubmitDescription(form, formData) {
    const formName = form.name || form.id || '表单';
    const fieldCount = formData.entries().length;
    
    return `用户提交了"${formName}"，包含${fieldCount}个字段`;
  }
  
  /**
   * 生成悬停行为描述
   * @param {Element} target 目标元素
   * @returns {string} 行为描述
   */
  generateHoverDescription(target) {
    const elementType = this.getElementType(target);
    const elementText = this.getElementText(target);
    
    return `用户将鼠标悬停在${elementType}"${elementText}"上`;
  }
  
  /**
   * 记录用户行为 - 优化版本
   * @param {Object} action 行为对象
   */
  recordAction(action) {
    // 防止重复记录相同的行为，增加间隔时间
    const now = Date.now();
    if (now - this.lastActionTime < 500) return; // 增加到500ms

    this.lastActionTime = now;
    this.actionQueue.push(action);

    console.log('📝 记录用户行为:', action.description); // 启用日志用于调试

    // 发送行为数据
    if (window.MessageBridge) {
      console.log('📤 发送行为数据到MessageBridge:', action.type);
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.BEHAVIOR_DETECTED, action);
    } else {
      console.warn('⚠️ MessageBridge不存在，无法发送行为数据');
    }

    // 限制队列长度
    if (this.actionQueue.length > 20) { // 减少队列长度
      this.actionQueue.shift();
    }
  }
  
  /**
   * 获取元素描述
   * @param {Element} element 元素
   * @returns {Object} 元素描述
   */
  getElementDescription(element) {
    return {
      tagName: element.tagName,
      id: element.id,
      className: element.className,
      text: this.getElementText(element)
    };
  }
  
  /**
   * 获取元素类型描述
   * @param {Element} element 元素
   * @returns {string} 类型描述
   */
  getElementType(element) {
    const tagName = element.tagName.toLowerCase();
    const typeMap = {
      'button': '按钮',
      'a': '链接',
      'input': '输入框',
      'textarea': '文本区域',
      'select': '下拉选择框',
      'div': '区域',
      'span': '文本',
      'img': '图片',
      'form': '表单'
    };
    
    return typeMap[tagName] || tagName;
  }
  
  /**
   * 获取元素文本内容
   * @param {Element} element 元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    let text = element.textContent || element.value || element.placeholder || element.alt || '';
    return text.trim().substring(0, 30);
  }
  
  /**
   * 查找输入框的标签
   * @param {Element} input 输入框元素
   * @returns {string} 标签文本
   */
  findLabelForInput(input) {
    // 通过for属性查找
    if (input.id) {
      const label = document.querySelector(`label[for="${input.id}"]`);
      if (label) return label.textContent.trim();
    }
    
    // 查找父级label
    const parentLabel = input.closest('label');
    if (parentLabel) return parentLabel.textContent.trim();
    
    return '';
  }
  
  /**
   * 判断是否为重要元素 - 增强版本，特别支持DeepSeek
   * @param {Element} element 元素
   * @returns {boolean} 是否重要
   */
  isImportantElement(element) {
    const importantTags = ['button', 'a', 'input', 'select', 'textarea'];
    const tagName = element.tagName.toLowerCase();

    // 基本标签检查
    if (importantTags.includes(tagName)) {
      return true;
    }

    // 检查是否有点击事件
    if (element.onclick !== null) {
      return true;
    }

    // 检查通用可点击类名
    if (element.classList.contains('clickable')) {
      return true;
    }

    // 检查role属性
    const role = element.getAttribute('role');
    if (role === 'button' || role === 'textbox' || role === 'link') {
      return true;
    }

    // 检查contenteditable属性
    if (element.contentEditable === 'true') {
      return true;
    }

    // DeepSeek特定元素检查
    const className = element.className || '';
    const deepSeekClasses = [
      '_6f28693',      // DeepSeek发送按钮
      '_7db3914',      // DeepSeek运行按钮
      '_5d271a3',      // 新发现的DeepSeek按钮class
      'ds-button',     // DeepSeek按钮基类
      'ds-icon',       // DeepSeek图标
      'ds-icon-button', // DeepSeek图标按钮
      'send-button',   // 发送按钮
      'run-button'     // 运行按钮
    ];

    if (deepSeekClasses.some(cls => className.includes(cls))) {
      console.log('🎯 发现DeepSeek重要元素:', element.tagName, className);
      return true;
    }

    // 检查是否包含重要的子元素（如图标）
    if (element.querySelector('.ds-icon') || element.querySelector('svg')) {
      return true;
    }

    // 检查文本内容是否包含重要关键词
    const text = element.textContent?.trim().toLowerCase() || '';
    const importantKeywords = ['发送', 'send', '运行', 'run', '执行', 'execute', '提交', 'submit'];
    if (importantKeywords.some(keyword => text.includes(keyword))) {
      return true;
    }

    return false;
  }
  
  /**
   * 开始追踪 - 优化版本，减少全局监听
   */
  startTracking() {
    this.isTracking = true;
    console.log('▶️ 开始用户行为追踪'); // 启用日志用于调试

    // 只启动轻量级追踪，不进行全局监听
    this.startLightweightTracking();
  }

  /**
   * 启动轻量级追踪 - 大幅减少性能开销
   */
  startLightweightTracking() {
    console.log('🌐 启动轻量级页面行为追踪'); // 启用日志用于调试

    // 不追踪整个document，只追踪特定区域
    // this.setupContainerListeners(document); // 禁用全局监听

    // 只关注关键的LLM内容区域
    this.trackKeyLLMContent();
  }

  /**
   * 追踪关键LLM内容 - 轻量级版本，增强对DeepSeek iframe的支持
   */
  trackKeyLLMContent() {
    console.log('🎯 开始追踪关键LLM内容...');

    // 只查找最关键的容器，减少查询范围
    const keyContainers = [
      // 只关注按钮和输入框
      document.querySelectorAll('button'),
      document.querySelectorAll('input[type="text"], input[type="search"], textarea'),
      // 只关注可编辑内容
      document.querySelectorAll('[contenteditable="true"]'),
      // 特别关注DeepSeek的特定元素
      document.querySelectorAll('div[role="button"]'),
      document.querySelectorAll('.ds-button'),
      document.querySelectorAll('.ds-icon-button'),
      document.querySelectorAll('._6f28693'), // DeepSeek发送按钮
      document.querySelectorAll('._7db3914'), // DeepSeek运行按钮
      document.querySelectorAll('._5d271a3'), // 新发现的DeepSeek按钮class
      document.querySelectorAll('._26c5bc2'), // DeepSeek代码预览容器
      // 关注iframe内容 - 特别关注DeepSeek的代码运行iframe
      document.querySelectorAll('iframe'),
      document.querySelectorAll('iframe[src*="run-html-chat.deepseeksvc.com"]'),
      document.querySelectorAll('iframe[src*="usercontent"]')
    ];

    keyContainers.forEach(nodeList => {
      // 限制处理数量，避免性能问题
      const limitedList = Array.from(nodeList).slice(0, 15); // 增加到15个
      limitedList.forEach(container => {
        if (!this.trackedContainers.has(container)) {
          console.log('🎯 发现关键LLM内容容器，开始追踪:', container.tagName, container.className);
          this.trackedContainers.add(container);
          this.setupLightweightListeners(container);

          // 如果是iframe，尝试监听其内容
          if (container.tagName === 'IFRAME') {
            this.setupIframeTracking(container);
          }
        }
      });
    });

    // 特别处理DeepSeek代码预览区域
    this.setupDeepSeekCodePreviewTracking();

    // 设置轻量级观察器
    this.setupLightweightContentObserver();
  }

  /**
   * 设置DeepSeek代码预览区域的特殊追踪
   */
  setupDeepSeekCodePreviewTracking() {
    console.log('🖼️ 设置DeepSeek代码预览区域追踪...');

    // 查找DeepSeek代码预览容器
    const codePreviewContainers = [
      document.querySelectorAll('._26c5bc2'), // DeepSeek代码预览主容器
      document.querySelectorAll('.fcd12e6e'), // DeepSeek iframe容器
      document.querySelectorAll('div[style*="width: var(--sidebar-width)"]') // 侧边栏容器
    ];

    codePreviewContainers.forEach(nodeList => {
      Array.from(nodeList).forEach(container => {
        if (!this.trackedContainers.has(container)) {
          console.log('🖼️ 发现DeepSeek代码预览容器:', container.className);
          this.trackedContainers.add(container);

          // 为整个容器设置监听
          this.setupLightweightListeners(container);

          // 查找容器内的iframe
          const iframes = container.querySelectorAll('iframe');
          iframes.forEach(iframe => {
            console.log('🖼️ 发现代码预览iframe:', iframe.src);
            this.setupIframeTracking(iframe);
          });

          // 监听容器内的动态变化
          this.observeContainerChanges(container);
        }
      });
    });
  }

  /**
   * 监听容器内的动态变化
   * @param {Element} container 容器元素
   */
  observeContainerChanges(container) {
    console.log('👀 开始监听容器动态变化...');

    const observer = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        // 检查新增的iframe
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.tagName === 'IFRAME') {
              console.log('🆕 检测到新的iframe:', node.src);
              this.setupIframeTracking(node);
            } else {
              // 检查新增节点内的iframe
              const iframes = node.querySelectorAll && node.querySelectorAll('iframe');
              if (iframes) {
                iframes.forEach(iframe => {
                  console.log('🆕 检测到新的嵌套iframe:', iframe.src);
                  this.setupIframeTracking(iframe);
                });
              }
            }
          }
        });
      });
    });

    observer.observe(container, {
      childList: true,
      subtree: true
    });

    // 存储observer以便后续清理
    if (!this.containerObservers) {
      this.containerObservers = new Map();
    }
    this.containerObservers.set(container, observer);
  }

  /**
   * 设置iframe追踪 - 处理DeepSeek等网站的iframe内容
   * @param {Element} iframe iframe元素
   */
  setupIframeTracking(iframe) {
    console.log('🖼️ 开始设置iframe追踪:', iframe.src || iframe.id || 'unknown');

    try {
      // 为iframe添加唯一标识
      if (!iframe.dataset.foreseeTracked) {
        iframe.dataset.foreseeTracked = 'true';
        iframe.dataset.foreseeId = 'iframe_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
      }

      // 特别处理DeepSeek代码运行iframe
      if (iframe.src && iframe.src.includes('run-html-chat.deepseeksvc.com')) {
        console.log('🖼️ 检测到DeepSeek代码运行iframe，使用特殊处理');
        this.setupDeepSeekIframeTracking(iframe);
      } else {
        // 设置通用多重监听策略
        this.setupMultipleIframeStrategies(iframe);
      }

      // 等待iframe加载完成
      iframe.addEventListener('load', () => {
        console.log('🖼️ iframe加载完成，重新设置监听');
        setTimeout(() => {
          if (iframe.src && iframe.src.includes('run-html-chat.deepseeksvc.com')) {
            this.setupDeepSeekIframeTracking(iframe);
          } else {
            this.setupMultipleIframeStrategies(iframe);
          }
        }, 500); // 延迟确保iframe内容完全加载
      });

      // 如果iframe已经加载完成，立即设置监听
      if (iframe.contentDocument || iframe.contentWindow) {
        setTimeout(() => {
          if (iframe.src && iframe.src.includes('run-html-chat.deepseeksvc.com')) {
            this.setupDeepSeekIframeTracking(iframe);
          } else {
            this.setupMultipleIframeStrategies(iframe);
          }
        }, 100);
      }

    } catch (error) {
      console.error('🖼️ 设置iframe追踪失败:', error);
    }
  }

  /**
   * 设置DeepSeek特定的iframe追踪
   * @param {Element} iframe iframe元素
   */
  setupDeepSeekIframeTracking(iframe) {
    console.log('🖼️ 设置DeepSeek特定iframe追踪:', iframe.src);

    // 1. 设置iframe层面的事件监听（捕获用户与iframe的交互）
    this.setupIframeEventCapture(iframe);

    // 2. 尝试注入监听脚本到iframe内容
    this.injectDeepSeekIframeScript(iframe);

    // 3. 设置跨域消息监听
    this.setupCrossOriginIframeTracking(iframe);

    // 4. 监听iframe的可见性变化
    this.observeIframeVisibility(iframe);

    // 5. 定期检查iframe内容变化
    this.startIframeContentPolling(iframe);
  }

  /**
   * 注入DeepSeek特定的iframe监听脚本
   * @param {Element} iframe iframe元素
   */
  injectDeepSeekIframeScript(iframe) {
    console.log('🖼️ 注入DeepSeek iframe监听脚本');

    try {
      // 创建专门针对DeepSeek代码运行环境的监听脚本
      const scriptContent = this.createDeepSeekIframeScript(iframe.dataset.foreseeId);

      // 尝试多种注入方式
      this.injectScriptToIframe(iframe, scriptContent);

      // 通过postMessage发送脚本
      if (iframe.contentWindow) {
        iframe.contentWindow.postMessage({
          source: 'FORESEE_DEEPSEEK_INJECTION',
          script: scriptContent,
          iframeId: iframe.dataset.foreseeId
        }, '*');
      }

    } catch (error) {
      console.log('🖼️ 注入DeepSeek iframe脚本失败:', error.message);
    }
  }

  /**
   * 创建DeepSeek特定的iframe监听脚本
   * @param {string} iframeId iframe唯一标识
   * @returns {string} 脚本内容
   */
  createDeepSeekIframeScript(iframeId) {
    return `
      (function() {
        console.log('🖼️ DeepSeek iframe监听脚本已注入:', '${iframeId}');

        // 监听所有点击事件（包括代码运行结果中的交互）
        document.addEventListener('click', function(event) {
          try {
            const target = event.target;
            console.log('🖼️ DeepSeek iframe内点击:', target.tagName, target.className, target.textContent?.substring(0, 50));

            const actionData = {
              type: 'deepseek_iframe_click',
              iframeId: '${iframeId}',
              target: {
                tagName: target.tagName,
                className: target.className,
                id: target.id,
                text: target.textContent?.substring(0, 50) || '',
                type: target.type || '',
                href: target.href || ''
              },
              coordinates: { x: event.clientX, y: event.clientY },
              timestamp: Date.now(),
              url: window.location.href
            };

            // 发送消息到父页面
            window.parent.postMessage({
              source: 'FORESEE_DEEPSEEK_TRACKER',
              data: actionData
            }, '*');

            console.log('🖼️ DeepSeek iframe点击事件已发送:', actionData);
          } catch (error) {
            console.error('🖼️ DeepSeek iframe点击事件处理失败:', error);
          }
        }, true);

        // 监听输入事件
        document.addEventListener('input', function(event) {
          try {
            const target = event.target;
            const value = target.value || target.textContent || '';

            if (value.length < 2) return;

            const actionData = {
              type: 'deepseek_iframe_input',
              iframeId: '${iframeId}',
              target: {
                tagName: target.tagName,
                className: target.className,
                id: target.id,
                type: target.type || 'text'
              },
              value: value.substring(0, 100),
              timestamp: Date.now(),
              url: window.location.href
            };

            window.parent.postMessage({
              source: 'FORESEE_DEEPSEEK_TRACKER',
              data: actionData
            }, '*');

            console.log('🖼️ DeepSeek iframe输入事件已发送:', actionData);
          } catch (error) {
            console.error('🖼️ DeepSeek iframe输入事件处理失败:', error);
          }
        }, true);

        // 监听表单提交
        document.addEventListener('submit', function(event) {
          try {
            const actionData = {
              type: 'deepseek_iframe_submit',
              iframeId: '${iframeId}',
              target: {
                tagName: event.target.tagName,
                className: event.target.className,
                id: event.target.id
              },
              timestamp: Date.now(),
              url: window.location.href
            };

            window.parent.postMessage({
              source: 'FORESEE_DEEPSEEK_TRACKER',
              data: actionData
            }, '*');

            console.log('🖼️ DeepSeek iframe提交事件已发送:', actionData);
          } catch (error) {
            console.error('🖼️ DeepSeek iframe提交事件处理失败:', error);
          }
        }, true);

        // 通知父页面脚本已就绪
        window.parent.postMessage({
          source: 'FORESEE_DEEPSEEK_TRACKER',
          data: { type: 'script_ready', iframeId: '${iframeId}' }
        }, '*');

        console.log('🖼️ DeepSeek iframe监听脚本初始化完成');
      })();
    `;
  }

  /**
   * 设置多重iframe监听策略
   * @param {Element} iframe iframe元素
   */
  setupMultipleIframeStrategies(iframe) {
    console.log('🖼️ 设置多重iframe监听策略');

    // 策略1: 尝试直接访问iframe内容（同源情况）
    this.tryDirectIframeAccess(iframe);

    // 策略2: 注入监听脚本到iframe（通过chrome扩展权限）
    this.injectIframeScript(iframe);

    // 策略3: 设置跨域postMessage通信
    this.setupCrossOriginIframeTracking(iframe);

    // 策略4: 监听iframe的鼠标和键盘事件（在父页面层面）
    this.setupIframeEventCapture(iframe);
  }

  /**
   * 尝试直接访问iframe内容
   * @param {Element} iframe iframe元素
   */
  tryDirectIframeAccess(iframe) {
    try {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (iframeDoc && iframeDoc.readyState === 'complete') {
        console.log('🖼️ 成功直接访问iframe内容');
        this.setupIframeContentListeners(iframeDoc, iframe);
        return true;
      }
    } catch (error) {
      console.log('🖼️ 直接访问iframe内容失败（跨域限制）:', error.message);
    }
    return false;
  }

  /**
   * 注入脚本到iframe
   * @param {Element} iframe iframe元素
   */
  injectIframeScript(iframe) {
    try {
      // 检查是否可以注入脚本
      if (iframe.contentWindow && typeof iframe.contentWindow.postMessage === 'function') {
        console.log('🖼️ 尝试向iframe注入监听脚本');

        // 创建监听脚本
        const scriptContent = this.createIframeListenerScript(iframe.dataset.foreseeId);

        // 尝试通过多种方式注入脚本
        this.injectScriptToIframe(iframe, scriptContent);
      }
    } catch (error) {
      console.log('🖼️ 注入iframe脚本失败:', error.message);
    }
  }

  /**
   * 创建iframe监听脚本内容
   * @param {string} iframeId iframe唯一标识
   * @returns {string} 脚本内容
   */
  createIframeListenerScript(iframeId) {
    return `
      (function() {
        console.log('🖼️ iframe监听脚本已注入:', '${iframeId}');

        // 监听点击事件
        document.addEventListener('click', function(event) {
          try {
            const target = event.target;
            const actionData = {
              type: 'iframe_click',
              iframeId: '${iframeId}',
              target: {
                tagName: target.tagName,
                className: target.className,
                id: target.id,
                text: target.textContent?.substring(0, 50) || ''
              },
              coordinates: { x: event.clientX, y: event.clientY },
              timestamp: Date.now()
            };

            // 发送消息到父页面
            window.parent.postMessage({
              source: 'FORESEE_IFRAME_TRACKER',
              data: actionData
            }, '*');

            console.log('🖼️ iframe点击事件已发送:', actionData);
          } catch (error) {
            console.error('🖼️ iframe点击事件处理失败:', error);
          }
        }, true);

        // 监听输入事件
        document.addEventListener('input', function(event) {
          try {
            const target = event.target;
            const value = target.value || target.textContent || '';

            if (value.length < 2) return; // 忽略太短的输入

            const actionData = {
              type: 'iframe_input',
              iframeId: '${iframeId}',
              target: {
                tagName: target.tagName,
                className: target.className,
                id: target.id,
                type: target.type || 'text'
              },
              value: value.substring(0, 100),
              timestamp: Date.now()
            };

            // 发送消息到父页面
            window.parent.postMessage({
              source: 'FORESEE_IFRAME_TRACKER',
              data: actionData
            }, '*');

            console.log('🖼️ iframe输入事件已发送:', actionData);
          } catch (error) {
            console.error('🖼️ iframe输入事件处理失败:', error);
          }
        }, true);

        // 通知父页面脚本已就绪
        window.parent.postMessage({
          source: 'FORESEE_IFRAME_TRACKER',
          data: { type: 'script_ready', iframeId: '${iframeId}' }
        }, '*');

      })();
    `;
  }

  /**
   * 注入脚本到iframe的具体实现
   * @param {Element} iframe iframe元素
   * @param {string} scriptContent 脚本内容
   */
  injectScriptToIframe(iframe, scriptContent) {
    try {
      // 方法1: 尝试直接在iframe中执行脚本
      if (iframe.contentWindow && iframe.contentDocument) {
        const script = iframe.contentDocument.createElement('script');
        script.textContent = scriptContent;
        iframe.contentDocument.head.appendChild(script);
        console.log('🖼️ 成功通过DOM注入iframe脚本');
        return true;
      }
    } catch (error) {
      console.log('🖼️ DOM注入失败，尝试其他方式:', error.message);
    }

    try {
      // 方法2: 通过Chrome扩展API注入独立脚本文件
      this.injectScriptViaExtensionAPI(iframe);
    } catch (error) {
      console.log('🖼️ 扩展API注入失败:', error.message);
    }

    try {
      // 方法3: 通过postMessage发送脚本内容
      if (iframe.contentWindow) {
        iframe.contentWindow.postMessage({
          source: 'FORESEE_SCRIPT_INJECTION',
          script: scriptContent
        }, '*');
        console.log('🖼️ 通过postMessage发送脚本');
        return true;
      }
    } catch (error) {
      console.log('🖼️ postMessage注入失败:', error.message);
    }

    return false;
  }

  /**
   * 通过Chrome扩展API注入脚本
   * @param {Element} iframe iframe元素
   */
  injectScriptViaExtensionAPI(iframe) {
    try {
      // 获取iframe的URL
      const iframeSrc = iframe.src;
      if (!iframeSrc || iframeSrc.startsWith('data:') || iframeSrc.startsWith('blob:')) {
        console.log('🖼️ iframe URL不支持扩展API注入:', iframeSrc);
        return;
      }

      // 通过background script注入脚本
      if (chrome && chrome.runtime) {
        chrome.runtime.sendMessage({
          type: 'INJECT_IFRAME_SCRIPT',
          data: {
            iframeUrl: iframeSrc,
            iframeId: iframe.dataset.foreseeId
          }
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.log('🖼️ 扩展API注入请求失败:', chrome.runtime.lastError.message);
          } else {
            console.log('🖼️ 扩展API注入请求已发送:', response);
          }
        });
      }
    } catch (error) {
      console.log('🖼️ 扩展API注入异常:', error.message);
    }
  }

  /**
   * 设置iframe事件捕获（在父页面层面）
   * @param {Element} iframe iframe元素
   */
  setupIframeEventCapture(iframe) {
    console.log('🖼️ 设置iframe事件捕获');

    // 监听iframe上的鼠标事件
    iframe.addEventListener('mousedown', (event) => {
      console.log('🖼️ iframe鼠标按下事件');
      this.handleIframeInteraction(event, iframe, 'mousedown');
    }, true);

    iframe.addEventListener('mouseup', (event) => {
      console.log('🖼️ iframe鼠标释放事件');
      this.handleIframeInteraction(event, iframe, 'mouseup');
    }, true);

    // 监听iframe的焦点事件
    iframe.addEventListener('focus', (event) => {
      console.log('🖼️ iframe获得焦点');
      this.handleIframeInteraction(event, iframe, 'focus');
    }, true);

    // 监听iframe的加载事件
    iframe.addEventListener('load', () => {
      console.log('🖼️ iframe加载完成，重新设置监听');
      setTimeout(() => {
        this.setupMultipleIframeStrategies(iframe);
      }, 1000);
    });
  }

  /**
   * 处理iframe交互事件
   * @param {Event} event 事件对象
   * @param {Element} iframe iframe元素
   * @param {string} eventType 事件类型
   */
  handleIframeInteraction(event, iframe, eventType) {
    const actionData = {
      type: 'iframe_interaction',
      eventType: eventType,
      target: {
        tagName: 'IFRAME',
        src: iframe.src,
        id: iframe.id,
        className: iframe.className
      },
      coordinates: { x: event.clientX, y: event.clientY },
      timestamp: Date.now(),
      description: `用户在iframe中进行了${eventType}操作`
    };

    console.log('🖼️ iframe交互事件:', actionData);
    this.recordAction(actionData);
  }

  /**
   * 设置iframe内容监听器
   * @param {Document} iframeDoc iframe文档
   * @param {Element} iframe iframe元素
   */
  setupIframeContentListeners(iframeDoc, iframe) {
    console.log('🖼️ 设置iframe内容监听器');

    // 为iframe内容设置点击监听
    iframeDoc.addEventListener('click', (event) => {
      console.log('🖼️ iframe内点击事件:', event.target);
      this.handleIframeClickAction(event, iframe);
    }, { passive: true, capture: true });

    // 为iframe内容设置输入监听
    iframeDoc.addEventListener('input', (event) => {
      console.log('🖼️ iframe内输入事件:', event.target);
      this.handleIframeInputAction(event, iframe);
    }, { passive: true });

    // 为iframe内容设置键盘监听
    iframeDoc.addEventListener('keydown', (event) => {
      console.log('🖼️ iframe内键盘事件:', event.key);
      this.handleIframeKeyboardAction(event, iframe);
    }, { passive: true });
  }

  /**
   * 设置跨域iframe追踪
   * @param {Element} iframe iframe元素
   */
  setupCrossOriginIframeTracking(iframe) {
    console.log('🖼️ 设置跨域iframe追踪');

    // 如果还没有设置全局消息监听器，则设置一个
    if (!this.globalMessageListenerSet) {
      this.setupGlobalMessageListener();
      this.globalMessageListenerSet = true;
    }

    // 存储iframe引用以便后续使用
    if (!this.trackedIframes) {
      this.trackedIframes = new Map();
    }
    this.trackedIframes.set(iframe.dataset.foreseeId, iframe);
  }

  /**
   * 监听iframe可见性变化
   * @param {Element} iframe iframe元素
   */
  observeIframeVisibility(iframe) {
    console.log('👀 开始监听iframe可见性变化');

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          console.log('🖼️ iframe变为可见，重新设置监听');
          // iframe变为可见时，重新尝试设置监听
          setTimeout(() => {
            this.injectDeepSeekIframeScript(iframe);
          }, 200);
        }
      });
    }, {
      threshold: 0.1 // 当iframe 10%可见时触发
    });

    observer.observe(iframe);

    // 存储observer以便后续清理
    if (!this.visibilityObservers) {
      this.visibilityObservers = new Map();
    }
    this.visibilityObservers.set(iframe, observer);
  }

  /**
   * 开始iframe内容轮询检查
   * @param {Element} iframe iframe元素
   */
  startIframeContentPolling(iframe) {
    console.log('🔄 开始iframe内容轮询检查');

    const iframeId = iframe.dataset.foreseeId;

    // 避免重复轮询
    if (this.pollingIntervals && this.pollingIntervals.has(iframeId)) {
      return;
    }

    const pollInterval = setInterval(() => {
      try {
        // 检查iframe是否仍然存在
        if (!document.contains(iframe)) {
          console.log('🖼️ iframe已被移除，停止轮询');
          clearInterval(pollInterval);
          if (this.pollingIntervals) {
            this.pollingIntervals.delete(iframeId);
          }
          return;
        }

        // 尝试重新注入脚本（如果iframe内容发生变化）
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage({
            source: 'FORESEE_DEEPSEEK_PING',
            iframeId: iframeId,
            timestamp: Date.now()
          }, '*');
        }

      } catch (error) {
        console.log('🖼️ iframe轮询检查出错:', error.message);
      }
    }, 3000); // 每3秒检查一次

    // 存储轮询间隔以便后续清理
    if (!this.pollingIntervals) {
      this.pollingIntervals = new Map();
    }
    this.pollingIntervals.set(iframeId, pollInterval);

    // 5分钟后停止轮询，避免长期占用资源
    setTimeout(() => {
      clearInterval(pollInterval);
      if (this.pollingIntervals) {
        this.pollingIntervals.delete(iframeId);
      }
      console.log('🖼️ iframe轮询检查已超时停止');
    }, 300000); // 5分钟
  }

  /**
   * 设置全局消息监听器
   */
  setupGlobalMessageListener() {
    console.log('🖼️ 设置全局iframe消息监听器');

    window.addEventListener('message', (event) => {
      try {
        // 检查消息来源 - 支持多种消息源
        if (event.data && (
          event.data.source === 'FORESEE_IFRAME_TRACKER' ||
          event.data.source === 'FORESEE_DEEPSEEK_TRACKER'
        )) {
          console.log('🖼️ 收到iframe追踪消息:', event.data);
          this.handleIframeTrackerMessage(event.data.data, event.source);
        }
      } catch (error) {
        console.error('🖼️ 处理iframe消息失败:', error);
      }
    });
  }

  /**
   * 处理iframe追踪消息
   * @param {Object} data 消息数据
   * @param {Window} _source 消息来源窗口
   */
  handleIframeTrackerMessage(data, _source) {
    console.log('🖼️ 处理iframe追踪消息:', data);

    // 根据消息类型处理
    switch (data.type) {
      case 'script_ready':
        console.log('🖼️ iframe脚本就绪:', data.iframeId);
        break;

      case 'iframe_click':
      case 'deepseek_iframe_click':
        this.handleIframeClickFromMessage(data);
        break;

      case 'iframe_input':
      case 'deepseek_iframe_input':
        this.handleIframeInputFromMessage(data);
        break;

      case 'deepseek_iframe_submit':
        this.handleIframeSubmitFromMessage(data);
        break;

      default:
        console.log('🖼️ 未知iframe消息类型:', data.type);
    }
  }

  /**
   * 处理来自iframe消息的提交事件
   * @param {Object} data 提交事件数据
   */
  handleIframeSubmitFromMessage(data) {
    console.log('🖼️ 处理iframe提交消息:', data);

    const actionDescription = `用户在iframe中提交了表单`;

    this.recordAction({
      type: 'submit',
      target: data.target,
      description: `[iframe内] ${actionDescription}`,
      timestamp: data.timestamp,
      iframe: {
        id: data.iframeId,
        type: 'deepseek_code_preview',
        url: data.url
      }
    });
  }

  /**
   * 处理来自iframe消息的点击事件
   * @param {Object} data 点击事件数据
   */
  handleIframeClickFromMessage(data) {
    console.log('🖼️ 处理iframe点击消息:', data);

    // 增强的描述生成，特别针对DeepSeek代码预览
    let actionDescription;
    if (data.type === 'deepseek_iframe_click') {
      actionDescription = `用户在DeepSeek代码预览中点击了${data.target.tagName}`;
      if (data.target.text) {
        actionDescription += `"${data.target.text}"`;
      }
      if (data.target.href) {
        actionDescription += `（链接: ${data.target.href}）`;
      }
    } else {
      actionDescription = `用户在iframe中点击了${data.target.tagName}元素"${data.target.text}"`;
    }

    this.recordAction({
      type: 'click',
      target: data.target,
      description: `[iframe内] ${actionDescription}`,
      timestamp: data.timestamp,
      coordinates: data.coordinates,
      iframe: {
        id: data.iframeId,
        type: data.type === 'deepseek_iframe_click' ? 'deepseek_code_preview' : 'cross_origin',
        url: data.url
      }
    });
  }

  /**
   * 处理来自iframe消息的输入事件
   * @param {Object} data 输入事件数据
   */
  handleIframeInputFromMessage(data) {
    console.log('🖼️ 处理iframe输入消息:', data);

    // 增强的描述生成，特别针对DeepSeek代码预览
    let actionDescription;
    if (data.type === 'deepseek_iframe_input') {
      actionDescription = `用户在DeepSeek代码预览的${data.target.tagName}中输入了内容`;
    } else {
      actionDescription = `用户在iframe的${data.target.tagName}中输入了内容`;
    }

    this.recordAction({
      type: 'input',
      target: data.target,
      description: `[iframe内] ${actionDescription}`,
      value: data.value,
      timestamp: data.timestamp,
      iframe: {
        id: data.iframeId,
        type: data.type === 'deepseek_iframe_input' ? 'deepseek_code_preview' : 'cross_origin',
        url: data.url
      }
    });
  }

  /**
   * 处理iframe内的键盘事件
   * @param {Event} event 键盘事件
   * @param {Element} iframe iframe元素
   */
  handleIframeKeyboardAction(event, iframe) {
    // 只记录重要的键盘事件
    const importantKeys = ['Enter', 'Tab', 'Escape', 'Space'];
    if (!importantKeys.includes(event.key)) {
      return;
    }

    const actionDescription = `用户在iframe中按下了${event.key}键`;

    this.recordAction({
      type: 'keyboard',
      target: {
        tagName: event.target.tagName,
        className: event.target.className,
        id: event.target.id,
        text: event.target.textContent?.substring(0, 30) || ''
      },
      description: `[iframe内] ${actionDescription}`,
      key: event.key,
      timestamp: Date.now(),
      iframe: {
        src: iframe.src,
        id: iframe.id,
        className: iframe.className
      }
    });
  }

  /**
   * 设置轻量级内容观察器 - 大幅减少性能开销
   */
  setupLightweightContentObserver() {
    if (this.contentObserver) {
      this.contentObserver.disconnect();
    }

    // 使用节流的观察器，大幅减少触发频率
    let observerTimeout = null;

    this.contentObserver = new MutationObserver((mutations) => {
      // 清除之前的定时器
      if (observerTimeout) {
        clearTimeout(observerTimeout);
      }

      // 减少节流时间到1秒，提高响应性
      observerTimeout = setTimeout(() => {
        // 只检查关键的新增元素
        const hasKeyElements = mutations.some(mutation => {
          return Array.from(mutation.addedNodes).some(node =>
            node.nodeType === Node.ELEMENT_NODE &&
            (node.tagName === 'BUTTON' ||
             node.tagName === 'INPUT' ||
             node.tagName === 'TEXTAREA' ||
             node.tagName === 'IFRAME' ||
             node.contentEditable === 'true' ||
             node.classList.contains('ds-button') ||
             node.classList.contains('ds-icon-button') ||
             node.classList.contains('_6f28693') ||
             node.classList.contains('_7db3914') ||
             node.classList.contains('_5d271a3'))
          );
        });

        if (hasKeyElements) {
          console.log('🔍 检测到新的关键元素，重新追踪');
          this.trackKeyLLMContent();
        }
      }, 1000); // 减少到1秒
    });

    // 只观察子节点变化，不观察属性和文本变化
    this.contentObserver.observe(document.body, {
      childList: true,
      subtree: true, // 改为观察子树，以便捕获深层变化
      attributes: false,
      characterData: false
    });
  }

  /**
   * 判断是否为潜在的LLM内容
   * @param {Element} element 元素
   * @returns {boolean} 是否为LLM内容
   */
  isPotentialLLMContent(element) {
    const className = element.className || '';
    const tagName = element.tagName.toLowerCase();

    // 检查类名
    const llmIndicators = ['message', 'chat', 'response', 'code', 'pre', 'output'];
    const hasLLMClass = llmIndicators.some(indicator =>
      className.toLowerCase().includes(indicator)
    );

    // 检查标签
    const llmTags = ['pre', 'code', 'iframe'];
    const isLLMTag = llmTags.includes(tagName);

    // 检查是否包含交互元素
    const hasInteractiveElements = element.querySelectorAll('button, input, a, [onclick]').length > 0;

    return hasLLMClass || isLLMTag || hasInteractiveElements;
  }
  
  /**
   * 停止追踪 - 优化版本，增强清理功能
   */
  stopTracking() {
    this.isTracking = false;
    console.log('⏹️ 停止用户行为追踪，开始清理资源...');

    // 清理内容观察器
    if (this.contentObserver) {
      this.contentObserver.disconnect();
      this.contentObserver = null;
    }

    // 清理容器观察器
    if (this.containerObservers) {
      this.containerObservers.forEach((observer, container) => {
        observer.disconnect();
        console.log('🗑️ 已清理容器观察器:', container.tagName);
      });
      this.containerObservers.clear();
    }

    // 清理可见性观察器
    if (this.visibilityObservers) {
      this.visibilityObservers.forEach((observer, iframe) => {
        observer.disconnect();
        console.log('🗑️ 已清理iframe可见性观察器:', iframe.src);
      });
      this.visibilityObservers.clear();
    }

    // 清理轮询间隔
    if (this.pollingIntervals) {
      this.pollingIntervals.forEach((interval, iframeId) => {
        clearInterval(interval);
        console.log('🗑️ 已清理iframe轮询:', iframeId);
      });
      this.pollingIntervals.clear();
    }

    // 清理所有追踪的容器
    this.trackedContainers.clear();

    // 清理iframe引用
    if (this.trackedIframes) {
      this.trackedIframes.clear();
    }

    console.log('✅ 用户行为追踪已停止，所有资源已清理');
  }
  
  /**
   * 获取行为队列
   * @returns {Array} 行为队列
   */
  getActionQueue() {
    return [...this.actionQueue];
  }
  
  /**
   * 清空行为队列 - 优化版本
   */
  clearActionQueue() {
    this.actionQueue = [];
    // console.log('🗑️ 行为队列已清空'); // 禁用日志
  }
}

// 创建行为追踪器实例
const behaviorTracker = new BehaviorTracker();

// 导出追踪器
window.BehaviorTracker = behaviorTracker;

// console.log('👁️ 用户行为追踪器模块已加载'); // 禁用日志
