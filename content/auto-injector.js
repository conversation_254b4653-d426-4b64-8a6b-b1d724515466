/**
 * 自动注入器 - 将用户行为描述自动注入到LLM对话框
 * 负责将追踪到的用户行为转换为文本并输入到聊天界面
 * 优化版本：减少日志输出，提高性能
 */

class AutoInjector {
  constructor() {
    this.currentSite = null;
    this.isAutoMode = false;
    this.pendingActions = [];
    this.injectionDelay = 2000; // 注入延迟，避免过于频繁
    this.lastInjectionTime = 0;

    // 防重复发送机制
    this.sendingState = {
      isSending: false,
      lastSentPrompt: null,
      lastSentTime: 0,
      sendingPromises: new Map(), // 存储正在发送的Promise
      promptHashes: new Set() // 存储已发送的prompt哈希值
    };

    this.init();
  }
  
  /**
   * 初始化自动注入器 - 优化版本，减少日志输出
   */
  init() {
    // console.log('💉 自动注入器初始化中...'); // 禁用日志

    // 获取当前网站配置
    this.currentSite = window.SiteConfigs?.getCurrentSiteConfig();

    // 监听用户行为事件
    if (window.MessageBridge) {
      window.MessageBridge.addListener(window.MESSAGE_TYPES.BEHAVIOR_DETECTED, (actionData) => {
        this.handleUserAction(actionData);
      });

      // 监听注入提示词命令
      window.MessageBridge.addListener(window.MESSAGE_TYPES.INJECT_PROMPT, (data) => {
        this.injectPrompt(data.prompt, data.autoSend);
      });
    }

    // console.log('✅ 自动注入器初始化完成'); // 禁用日志
  }
  
  /**
   * 处理用户行为 - 优化版本，减少日志输出
   * @param {Object} actionData 行为数据
   */
  handleUserAction(actionData) {
    if (!this.isAutoMode) {
      // console.log('ℹ️ 自动模式未开启，跳过行为注入'); // 禁用日志
      return;
    }

    // console.log('🎯 处理用户行为:', actionData.description); // 禁用日志

    // 将行为添加到待处理队列
    this.pendingActions.push(actionData);

    // 防抖处理，避免频繁注入
    clearTimeout(this.injectionTimeout);
    this.injectionTimeout = setTimeout(() => {
      this.processPendingActions();
    }, this.injectionDelay);
  }
  
  /**
   * 处理待处理的行为队列
   */
  processPendingActions() {
    if (this.pendingActions.length === 0) return;
    
    // 合并多个行为为一个描述
    const behaviorDescription = this.generateBehaviorSummary(this.pendingActions);
    
    // 注入到对话框
    this.injectBehaviorDescription(behaviorDescription);
    
    // 清空队列
    this.pendingActions = [];
  }
  
  /**
   * 生成行为摘要
   * @param {Array} actions 行为数组
   * @returns {string} 行为摘要
   */
  generateBehaviorSummary(actions) {
    if (actions.length === 1) {
      return actions[0].description;
    }
    
    // 按类型分组
    const groupedActions = this.groupActionsByType(actions);
    const summaryParts = [];
    
    // 生成各类型的摘要
    Object.keys(groupedActions).forEach(type => {
      const typeActions = groupedActions[type];
      const typeSummary = this.generateTypeSummary(type, typeActions);
      if (typeSummary) {
        summaryParts.push(typeSummary);
      }
    });
    
    return summaryParts.join('；');
  }
  
  /**
   * 按类型分组行为
   * @param {Array} actions 行为数组
   * @returns {Object} 分组后的行为
   */
  groupActionsByType(actions) {
    const groups = {};
    
    actions.forEach(action => {
      if (!groups[action.type]) {
        groups[action.type] = [];
      }
      groups[action.type].push(action);
    });
    
    return groups;
  }
  
  /**
   * 生成特定类型的摘要
   * @param {string} type 行为类型
   * @param {Array} actions 该类型的行为数组
   * @returns {string} 类型摘要
   */
  generateTypeSummary(type, actions) {
    switch (type) {
      case 'click':
        if (actions.length === 1) {
          return actions[0].description;
        } else {
          return `用户进行了${actions.length}次点击操作`;
        }
      
      case 'input':
        const lastInput = actions[actions.length - 1];
        return `用户在输入框中输入了: ${lastInput.value}`;
      
      case 'submit':
        return `用户提交了表单`;
      
      case 'scroll':
        const lastScroll = actions[actions.length - 1];
        return `用户滚动页面到${lastScroll.scrollPercentage}%位置`;
      
      default:
        return actions[actions.length - 1].description;
    }
  }
  
  /**
   * 注入行为描述到对话框
   * @param {string} description 行为描述
   */
  injectBehaviorDescription(description) {
    if (!this.currentSite) {
      console.error('❌ 当前网站不支持自动注入');
      return;
    }

    const inputElement = this.currentSite.getInputElement();
    if (!inputElement) {
      console.error('❌ 找不到输入框元素');
      return;
    }

    // 构造完整的消息
    const message = `用户操作反馈: ${description}`;

    // 注入文本
    this.injectText(inputElement, message);

    // 记录注入时间
    this.lastInjectionTime = Date.now();

    // console.log('💉 已注入行为描述:', message); // 禁用日志

    // 通知注入完成
    if (window.MessageBridge) {
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.PROMPT_INJECTED, {
        message: message,
        timestamp: this.lastInjectionTime
      });
    }
  }

  /**
   * 追加用户行为反馈到输入框（不自动发送）
   * @param {string} feedbackMessage 反馈消息
   */
  async appendBehaviorFeedback(feedbackMessage) {
    console.log('📝 开始追加用户行为反馈到输入框...');

    if (!this.currentSite) {
      console.error('❌ 当前网站不支持自动注入');
      return;
    }

    try {
      // 尝试获取输入框元素，支持重试
      let inputElement = await this.getInputElementWithRetry();

      if (!inputElement) {
        console.error('❌ 找不到输入框元素');
        return;
      }

      console.log('✅ 找到输入框元素，准备追加反馈');

      // 追加文本到输入框
      const success = this.appendText(inputElement, feedbackMessage);

      if (success) {
        console.log('✅ 用户行为反馈已成功追加到输入框');

        // 记录注入时间
        this.lastInjectionTime = Date.now();

        // 通知追加完成
        if (window.MessageBridge) {
          window.MessageBridge.sendMessage(window.MESSAGE_TYPES.PROMPT_INJECTED, {
            message: feedbackMessage,
            timestamp: this.lastInjectionTime,
            type: 'behavior_feedback',
            autoSend: false
          });
        }
      } else {
        console.error('❌ 追加用户行为反馈失败');
      }

    } catch (error) {
      console.error('❌ 追加用户行为反馈时出错:', error);
    }
  }
  
  /**
   * 生成提示词哈希值
   * @param {string} prompt 提示词内容
   * @returns {string} 哈希值
   */
  generatePromptHash(prompt) {
    // 简单的哈希函数，用于检测重复
    let hash = 0;
    for (let i = 0; i < prompt.length; i++) {
      const char = prompt.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 检查是否为重复发送
   * @param {string} prompt 提示词内容
   * @returns {boolean} 是否重复
   */
  isDuplicateSend(prompt) {
    const promptHash = this.generatePromptHash(prompt);
    const now = Date.now();

    // 检查是否在短时间内发送了相同的prompt
    if (this.sendingState.lastSentPrompt === prompt &&
        now - this.sendingState.lastSentTime < 5000) { // 5秒内
      // console.warn('⚠️ 检测到重复发送，跳过:', prompt.substring(0, 50) + '...'); // 禁用日志
      return true;
    }

    // 检查哈希值是否已存在
    if (this.sendingState.promptHashes.has(promptHash)) {
      // console.warn('⚠️ 检测到相同内容的prompt已发送，跳过'); // 禁用日志
      return true;
    }

    return false;
  }

  /**
   * 记录发送状态
   * @param {string} prompt 提示词内容
   */
  recordSendingState(prompt) {
    const promptHash = this.generatePromptHash(prompt);
    const now = Date.now();

    this.sendingState.lastSentPrompt = prompt;
    this.sendingState.lastSentTime = now;
    this.sendingState.promptHashes.add(promptHash);

    // 清理过期的哈希值（保留最近1小时的）
    if (this.sendingState.promptHashes.size > 100) {
      // 简单的清理策略：当数量过多时清空一半
      const hashArray = Array.from(this.sendingState.promptHashes);
      this.sendingState.promptHashes.clear();
      hashArray.slice(-50).forEach(hash => {
        this.sendingState.promptHashes.add(hash);
      });
    }
  }

  /**
   * 注入提示词（增强版，支持重试和等待，防重复发送）- 优化版本，减少日志输出
   * @param {string} prompt 提示词内容
   * @param {boolean} autoSend 是否自动发送
   */
  async injectPrompt(prompt, autoSend = false) {
    // console.log('💉 开始注入提示词，长度:', prompt.length); // 禁用日志

    // 检查是否为重复发送
    if (this.isDuplicateSend(prompt)) {
      // console.log('🚫 跳过重复发送的提示词'); // 禁用日志
      this.notifyInjectionFailed('重复发送被阻止');
      return;
    }

    // 检查是否正在发送中
    if (this.sendingState.isSending) {
      // console.log('⏳ 正在发送中，等待当前发送完成...'); // 禁用日志

      // 等待当前发送完成
      const existingPromise = this.sendingState.sendingPromises.get('current');
      if (existingPromise) {
        try {
          await existingPromise;
        } catch (error) {
          console.warn('⚠️ 等待当前发送完成时出错:', error.message);
        }
      }
    }

    // 设置发送状态
    this.sendingState.isSending = true;

    // 创建发送Promise
    const sendingPromise = this.executeInjectPrompt(prompt, autoSend);
    this.sendingState.sendingPromises.set('current', sendingPromise);

    try {
      await sendingPromise;
      // 记录发送状态
      this.recordSendingState(prompt);
    } catch (error) {
      console.error('❌ 注入提示词失败:', error);
      this.notifyInjectionFailed(error.message);
    } finally {
      // 清理发送状态
      this.sendingState.isSending = false;
      this.sendingState.sendingPromises.delete('current');
    }
  }

  /**
   * 执行提示词注入的核心逻辑
   * @param {string} prompt 提示词内容
   * @param {boolean} autoSend 是否自动发送
   */
  async executeInjectPrompt(prompt, autoSend = false) {
    if (!this.currentSite) {
      throw new Error('当前网站不支持提示词注入');
    }

    // console.log('🔍 当前网站配置:', this.currentSite.name); // 禁用日志

    // 尝试获取输入框元素，支持重试
    let inputElement = await this.getInputElementWithRetry();

    if (!inputElement) {
      throw new Error('找不到输入框元素');
    }

    // console.log('✅ 找到输入框元素:', { // 禁用日志
    //   id: inputElement.id,
    //   className: inputElement.className,
    //   placeholder: inputElement.placeholder,
    //   tagName: inputElement.tagName
    // });

    // 注入提示词
    const success = this.injectText(inputElement, prompt);

    if (!success) {
      throw new Error('文本注入失败');
    }

    // console.log('💉 已成功注入提示词'); // 禁用日志

    // 如果需要自动发送
    if (autoSend) {
      // console.log('🚀 准备自动发送消息...'); // 禁用日志
      setTimeout(async () => {
        try {
          const sendSuccess = await this.sendMessageWithRetry();
          if (sendSuccess) {
            // 发送成功后，启动代码运行监控
            this.startCodeRunMonitoring();
          }
        } catch (error) {
          console.error('❌ 自动发送失败:', error);
        }
      }, 1500); // 增加延迟时间，确保文本已完全注入
    }

    // 通知注入完成
    if (window.MessageBridge) {
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.PROMPT_INJECTED, {
        prompt: prompt,
        autoSend: autoSend,
        timestamp: Date.now(),
        success: true
      });
    }
  }

  /**
   * 启动代码运行监控
   */
  startCodeRunMonitoring() {
    console.log('🔍 启动代码运行监控...');

    // 等待一段时间让AI生成代码
    setTimeout(() => {
      this.checkForRunButtons();
    }, 3000); // 3秒后开始检查运行按钮

    // 设置定期检查
    this.runButtonCheckInterval = setInterval(() => {
      this.checkForRunButtons();
    }, 2000); // 每2秒检查一次

    // 10分钟后停止检查
    setTimeout(() => {
      if (this.runButtonCheckInterval) {
        clearInterval(this.runButtonCheckInterval);
        this.runButtonCheckInterval = null;
        console.log('⏰ 代码运行监控已超时停止');
      }
    }, 600000); // 10分钟
  }

  /**
   * 检查并自动点击运行按钮
   */
  async checkForRunButtons() {
    if (!this.currentSite) return;

    console.log('🔍 检查页面上的运行按钮...');

    try {
      const runButtons = this.findRunButtons();

      if (runButtons.length > 0) {
        console.log(`✅ 找到 ${runButtons.length} 个运行按钮`);

        // 详细记录每个找到的按钮
        runButtons.forEach((button, index) => {
          console.log(`🔍 运行按钮 ${index + 1}:`, {
            text: button.textContent.trim(),
            className: button.className,
            tagName: button.tagName,
            disabled: button.disabled || button.classList.contains('ds-button--disabled')
          });
        });

        // 直接点击最后一个运行按钮（兜底策略）
        const targetButton = runButtons[runButtons.length - 1];
        console.log('🎯 准备点击最后一个运行按钮:', targetButton.textContent.trim());

        await this.clickRunButton(targetButton);

        // 停止监控，因为已经找到并点击了按钮
        if (this.runButtonCheckInterval) {
          clearInterval(this.runButtonCheckInterval);
          this.runButtonCheckInterval = null;
          console.log('✅ 已点击运行按钮，停止监控');
        }
      }
    } catch (error) {
      console.error('❌ 检查运行按钮时出错:', error);
    }
  }

  /**
   * 查找页面上的运行按钮
   * @returns {Array<Element>} 运行按钮数组
   */
  findRunButtons() {
    console.log('🔍 开始查找运行按钮...');

    // 直接查找所有包含"运行"文本的按钮
    const allButtons = document.querySelectorAll('div[role="button"], button');
    const runButtons = [];

    console.log(`🔍 页面上共找到 ${allButtons.length} 个按钮`);

    allButtons.forEach((button, index) => {
      const text = button.textContent.trim();
      const hasRunText = text.includes('运行') || text.includes('Run') || text.includes('执行') || text.includes('Execute');

      console.log(`🔍 按钮 ${index + 1}: "${text}" - 包含运行文本: ${hasRunText}`);

      if (hasRunText && this.isValidRunButton(button)) {
        runButtons.push(button);
        console.log(`✅ 添加运行按钮: "${text}"`);
      }
    });

    console.log(`🔍 最终找到 ${runButtons.length} 个有效的运行按钮`);

    // 如果找到多个运行按钮，选择最后一个（根据你的要求）
    if (runButtons.length > 1) {
      console.log('🎯 找到多个运行按钮，选择最后一个作为兜底策略');
      return [runButtons[runButtons.length - 1]];
    }

    return runButtons;
  }

  /**
   * 通过文本查找按钮
   * @param {string} selector 包含:contains的选择器
   * @returns {Array<Element>} 匹配的按钮
   */
  findButtonsByText(selector) {
    const buttons = [];

    // 解析选择器
    const match = selector.match(/^(.+):contains\("(.+)"\)$/);
    if (!match) return buttons;

    const baseSelector = match[1];
    const searchText = match[2];

    // 查找基础元素
    const elements = document.querySelectorAll(baseSelector);

    elements.forEach(element => {
      if (element.textContent.includes(searchText)) {
        buttons.push(element);
      }
    });

    return buttons;
  }

  /**
   * 验证是否为有效的运行按钮 - 优化版本，减少日志输出
   * @param {Element} element 要验证的元素
   * @returns {boolean} 是否有效
   */
  isValidRunButton(element) {
    const text = element.textContent.trim().toLowerCase();

    // 检查元素是否可见
    if (!window.SiteConfigs?.isElementVisible(element)) {
      return false;
    }

    // 必须包含运行相关的文本
    const validTexts = ['运行', 'run', '执行', 'execute'];
    const hasValidText = validTexts.some(validText => text.includes(validText));

    if (!hasValidText) {
      return false;
    }

    // 排除明确不是运行按钮的文本
    const excludeTexts = ['复制', 'copy', '下载', 'download', '分享', 'share', '编辑', 'edit'];
    const isExcluded = excludeTexts.some(excludeText => text.includes(excludeText));

    if (isExcluded) {
      return false;
    }

    // 对于禁用的运行按钮，我们仍然认为它是有效的运行按钮
    // 因为它可能在代码生成后会变为可用状态
    const isDisabled = element.disabled ||
                      element.getAttribute('aria-disabled') === 'true' ||
                      element.classList.contains('ds-button--disabled');

    if (isDisabled) {
      // console.log('⚠️ 运行按钮当前被禁用，但仍认为是有效的运行按钮:', text); // 禁用日志
    }

    // console.log('✅ 找到有效运行按钮:', text); // 禁用日志
    return true;
  }

  /**
   * 点击运行按钮 - 优化版本，减少日志输出
   * @param {Element} button 运行按钮元素
   */
  async clickRunButton(button) {
    // console.log('🏃 准备点击运行按钮:', { // 禁用日志
    //   text: button.textContent.trim(),
    //   className: button.className,
    //   tagName: button.tagName,
    //   disabled: button.disabled || button.classList.contains('ds-button--disabled')
    // });

    try {
      // 滚动到按钮位置
      button.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // 等待一小段时间确保滚动完成
      await new Promise(resolve => setTimeout(resolve, 500));

      // 检查按钮是否被禁用
      const isDisabled = button.disabled ||
                        button.getAttribute('aria-disabled') === 'true' ||
                        button.classList.contains('ds-button--disabled');

      if (isDisabled) {
        // console.log('⏳ 运行按钮被禁用，等待按钮变为可用...'); // 禁用日志

        // 等待按钮变为可用，最多等待30秒
        const enabledButton = await this.waitForRunButtonEnabled(button, 30000);
        if (!enabledButton) {
          console.warn('⚠️ 运行按钮在等待时间内未变为可用，尝试强制点击');
          // 即使禁用也尝试点击，有些网站的禁用只是样式
        }
      }

      // 点击按钮
      button.click();

      // console.log('✅ 运行按钮已点击'); // 禁用日志

      // 通知运行按钮被点击
      if (window.MessageBridge) {
        window.MessageBridge.sendMessage(window.MESSAGE_TYPES.RUN_BUTTON_CLICKED, {
          button: {
            text: button.textContent.trim(),
            className: button.className,
            tagName: button.tagName
          },
          timestamp: Date.now()
        });
      }

    } catch (error) {
      console.error('❌ 点击运行按钮失败:', error);
      throw error;
    }
  }

  /**
   * 等待运行按钮变为可用
   * @param {Element} button 按钮元素
   * @param {number} timeout 超时时间（毫秒）
   * @returns {Promise<Element|null>} 可用的按钮或null
   */
  async waitForRunButtonEnabled(button, timeout = 30000) {
    console.log('⏳ 等待运行按钮变为可用...');

    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const isDisabled = button.disabled ||
                        button.getAttribute('aria-disabled') === 'true' ||
                        button.classList.contains('ds-button--disabled');

      if (!isDisabled) {
        console.log('✅ 运行按钮已变为可用');
        return button;
      }

      await new Promise(resolve => setTimeout(resolve, 1000)); // 每秒检查一次
    }

    console.warn('⏰ 等待运行按钮可用超时');
    return null;
  }

  /**
   * 获取输入框元素（支持重试）- 优化版本，减少日志输出
   * @param {number} maxRetries 最大重试次数
   * @param {number} retryDelay 重试延迟（毫秒）
   * @returns {Promise<Element|null>} 输入框元素或null
   */
  async getInputElementWithRetry(maxRetries = 3, retryDelay = 2000) {
    // console.log(`🔄 开始查找输入框元素，最多重试 ${maxRetries} 次...`); // 禁用日志

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      // console.log(`🔍 第 ${attempt}/${maxRetries} 次尝试查找输入框...`); // 禁用日志

      // 尝试立即获取
      let inputElement = this.currentSite.getInputElement();

      if (inputElement) {
        // console.log(`✅ 第 ${attempt} 次尝试成功找到输入框`); // 禁用日志
        return inputElement;
      }

      // 如果立即获取失败，尝试等待元素出现
      // console.log(`⏳ 第 ${attempt} 次尝试失败，等待元素出现...`); // 禁用日志
      inputElement = await window.SiteConfigs?.waitForElement(
        () => this.currentSite.getInputElement(),
        retryDelay,
        500
      );

      if (inputElement) {
        // console.log(`✅ 第 ${attempt} 次尝试通过等待成功找到输入框`); // 禁用日志
        return inputElement;
      }

      if (attempt < maxRetries) {
        // console.log(`⏳ 第 ${attempt} 次尝试失败，${retryDelay}ms 后重试...`); // 禁用日志
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    console.error(`❌ 经过 ${maxRetries} 次重试后仍未找到输入框元素`);
    return null;
  }

  /**
   * 发送消息（支持重试）- 优化版本，减少日志输出
   * @param {number} maxRetries 最大重试次数
   * @returns {Promise<boolean>} 是否发送成功
   */
  async sendMessageWithRetry(maxRetries = 2) {
    // console.log(`🚀 开始发送消息，最多重试 ${maxRetries} 次...`); // 禁用日志

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      // console.log(`📤 第 ${attempt}/${maxRetries} 次尝试发送消息...`); // 禁用日志

      const success = await this.sendMessage();

      if (success) {
        // console.log(`✅ 第 ${attempt} 次尝试发送成功`); // 禁用日志
        return true;
      }

      if (attempt < maxRetries) {
        // console.log(`⏳ 第 ${attempt} 次发送失败，1秒后重试...`); // 禁用日志
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.error(`❌ 经过 ${maxRetries} 次重试后仍无法发送消息`);
    return false;
  }

  /**
   * 通知注入失败
   * @param {string} reason 失败原因
   */
  notifyInjectionFailed(reason) {
    console.error('💉 提示词注入失败:', reason);

    // 检查是否是连接错误
    if (reason.includes('Could not establish connection')) {
      console.warn('⚠️ 检测到连接错误，可能是扩展重新加载导致');
      this.showUserNotification('扩展连接断开，请刷新页面后重试', 'error');
      return;
    }

    // 尝试发送消息，如果失败则直接显示用户通知
    try {
      // 通知注入失败
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.PROMPT_INJECTED, {
        success: false,
        error: reason,
        timestamp: Date.now()
      });

      // 发送状态更新
      window.MessageBridge.sendStatusUpdate({
        error: true,
        message: `注入失败: ${reason}`
      });
    } catch (error) {
      console.error('❌ 发送失败通知时出错:', error);
      this.showUserNotification(`注入失败: ${reason}`, 'error');
    }
  }

  /**
   * 显示用户通知
   * @param {string} message 通知消息
   * @param {string} type 通知类型 ('success', 'error', 'warning', 'info')
   */
  showUserNotification(message, type = 'info') {
    const notification = document.createElement('div');

    const colors = {
      success: '#4CAF50',
      error: '#f44336',
      warning: '#ff9800',
      info: '#2196F3'
    };

    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${colors[type] || colors.info};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      z-index: 10000;
      font-family: Arial, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      max-width: 300px;
      word-wrap: break-word;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);

    console.log(`📢 用户通知 [${type}]:`, message);
  }
  
  /**
   * 追加文本到输入框（专门用于追加，不替换现有内容）
   * @param {Element} inputElement 输入框元素
   * @param {string} text 要追加的文本
   * @returns {boolean} 是否追加成功
   */
  appendText(inputElement, text) {
    try {
      console.log('📝 开始追加文本到输入框...');

      // 检查输入框是否有效
      if (!inputElement) {
        console.error('❌ 输入框元素无效');
        return false;
      }

      // 获取当前值
      const currentValue = inputElement.value || inputElement.textContent || '';

      // 检查是否已经包含相同的文本（防止重复追加）
      if (currentValue.includes(text)) {
        console.log('⚠️ 输入框已包含相同文本，跳过追加');
        return true;
      }

      // 构造新值：总是追加到现有内容
      let newValue;
      if (currentValue.trim() === '') {
        // 如果输入框为空，直接设置
        newValue = text;
      } else {
        // 追加到现有内容，用换行分隔
        newValue = `${currentValue}\n\n${text}`;
      }

      console.log('📝 准备追加的文本长度:', text.length);
      console.log('📝 追加后总长度:', newValue.length);

      // 设置新值
      this.setElementValue(inputElement, newValue);

      // 触发输入事件
      this.triggerInputEvents(inputElement);

      // 设置焦点和光标位置到末尾
      this.setFocusAndCursor(inputElement, newValue);

      console.log('✅ 文本追加成功');
      return true;

    } catch (error) {
      console.error('❌ 文本追加过程中发生错误:', error);
      return false;
    }
  }

  /**
   * 注入文本到输入框（增强版，返回成功状态）- 优化版本，减少日志输出
   * @param {Element} inputElement 输入框元素
   * @param {string} text 要注入的文本
   * @returns {boolean} 是否注入成功
   */
  injectText(inputElement, text) {
    try {
      // console.log('📝 开始注入文本到输入框...'); // 禁用日志

      // 检查输入框是否有效
      if (!inputElement) {
        console.error('❌ 输入框元素无效');
        return false;
      }

      // 检查当前值
      const currentValue = inputElement.value || inputElement.textContent || '';

      // 检查是否已经包含相同的文本（防止重复注入）
      if (currentValue.includes(text)) {
        // console.log('⚠️ 输入框已包含相同文本，跳过注入'); // 禁用日志
        return true;
      }

      // 智能决定是追加还是替换
      let newValue;
      if (currentValue.trim() === '') {
        // 如果输入框为空，直接设置
        newValue = text;
      } else if (text.startsWith('用户反馈：') || text.startsWith('🎯 用户')) {
        // 如果是用户反馈，追加到现有内容
        newValue = `${currentValue}\n\n${text}`;
      } else {
        // 其他情况（如初始提示词），替换现有内容
        newValue = text;
      }

      // console.log('📝 准备注入的文本长度:', newValue.length); // 禁用日志

      // 设置新值
      this.setElementValue(inputElement, newValue);

      // 触发输入事件
      this.triggerInputEvents(inputElement);

      // 设置焦点和光标位置
      this.setFocusAndCursor(inputElement, newValue);

      // console.log('✅ 文本注入成功'); // 禁用日志
      return true;

    } catch (error) {
      console.error('❌ 文本注入过程中发生错误:', error);
      return false;
    }
  }
  
  /**
   * 触发输入相关事件
   * @param {Element} element 输入元素
   */
  triggerInputEvents(element) {
    // 创建并触发各种事件，确保框架能够检测到变化
    const events = ['input', 'change', 'keyup', 'paste'];
    
    events.forEach(eventType => {
      const event = new Event(eventType, {
        bubbles: true,
        cancelable: true
      });
      element.dispatchEvent(event);
    });
    
    // 特殊处理React等框架
    if (element._valueTracker) {
      element._valueTracker.setValue('');
    }
    
    // 触发React的onChange
    const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
      window.HTMLTextAreaElement.prototype,
      'value'
    ).set;
    
    if (nativeInputValueSetter) {
      nativeInputValueSetter.call(element, element.value);
      
      const inputEvent = new Event('input', { bubbles: true });
      element.dispatchEvent(inputEvent);
    }
  }
  
  /**
   * 发送消息（异步版本，支持等待和重试）- 优化版本，减少日志输出
   * @returns {Promise<boolean>} 是否发送成功
   */
  async sendMessage() {
    // console.log('📤 开始发送消息'); // 禁用日志

    if (!this.currentSite) {
      console.error('❌ 当前网站不支持自动发送');
      return false;
    }

    // 尝试获取发送按钮，支持等待
    let sendButton = this.currentSite.getSendButton();

    if (!sendButton) {
      // console.log('⏳ 未立即找到发送按钮，等待按钮出现...'); // 禁用日志
      sendButton = await window.SiteConfigs?.waitForElement(
        () => this.currentSite.getSendButton(),
        3000,
        500
      );
    }

    if (!sendButton) {
      // console.log('🔍 尝试智能查找发送按钮...'); // 禁用日志
      sendButton = await this.findSendButtonIntelligently();
    }

    if (!sendButton) {
      console.error('❌ 经过多种尝试后仍找不到发送按钮');
      return false;
    }

    // console.log('✅ 找到发送按钮:', { // 禁用日志
    //   text: sendButton.textContent.trim(),
    //   type: sendButton.type,
    //   disabled: sendButton.disabled,
    //   className: sendButton.className
    // });

    // 检查按钮是否可用
    if (sendButton.disabled) {
      // console.warn('⚠️ 发送按钮当前不可用，等待按钮变为可用...'); // 禁用日志

      // 等待按钮变为可用
      const enabledButton = await this.waitForButtonEnabled(sendButton, 5000);
      if (!enabledButton) {
        console.error('❌ 发送按钮在等待时间内未变为可用');
        return false;
      }
      sendButton = enabledButton;
    }

    try {
      // 点击发送按钮
      // console.log('🎯 点击发送按钮...'); // 禁用日志
      sendButton.click();

      // 等待一小段时间确保点击生效
      await new Promise(resolve => setTimeout(resolve, 500));

      // console.log('📤 已成功发送消息'); // 禁用日志
      return true;

    } catch (error) {
      console.error('❌ 点击发送按钮时发生错误:', error);
      return false;
    }
  }

  /**
   * 智能查找发送按钮
   * @returns {Promise<Element|null>} 发送按钮元素或null
   */
  async findSendButtonIntelligently() {
    console.log('🧠 开始智能查找发送按钮...');

    // 尝试更多的查找方式
    const allButtons = document.querySelectorAll('button');
    console.log('🔍 页面上找到的所有按钮:', allButtons.length);

    // 查找可能的发送按钮
    const possibleSendButtons = [];
    allButtons.forEach((button, index) => {
      const buttonText = button.textContent.trim().toLowerCase();
      const isDisabled = button.disabled;
      const hasSubmitType = button.type === 'submit';
      const hasSvg = button.querySelector('svg');
      const hasAriaLabel = button.getAttribute('aria-label');
      const hasTitle = button.getAttribute('title');

      // 评分系统：根据不同特征给按钮打分
      let score = 0;

      if (hasSubmitType) score += 10;
      if (hasSvg) score += 8;
      if (buttonText.includes('发送') || buttonText.includes('send')) score += 15;
      if (buttonText.includes('提交') || buttonText.includes('submit')) score += 12;
      if (hasAriaLabel && (hasAriaLabel.includes('发送') || hasAriaLabel.includes('send'))) score += 10;
      if (hasTitle && (hasTitle.includes('发送') || hasTitle.includes('send'))) score += 10;
      if (!isDisabled) score += 5;
      if (window.SiteConfigs.isElementVisible(button)) score += 5;

      if (score > 0) {
        possibleSendButtons.push({
          index,
          element: button,
          text: buttonText,
          type: button.type,
          disabled: isDisabled,
          hasSvg: !!hasSvg,
          score: score,
          ariaLabel: hasAriaLabel,
          title: hasTitle
        });
      }
    });

    // 按分数排序
    possibleSendButtons.sort((a, b) => b.score - a.score);

    console.log('🔍 可能的发送按钮（按分数排序）:', possibleSendButtons.slice(0, 5));

    // 返回分数最高的可用按钮
    for (let buttonInfo of possibleSendButtons) {
      if (!buttonInfo.disabled && window.SiteConfigs.isElementVisible(buttonInfo.element)) {
        console.log('🎯 选择最佳发送按钮:', buttonInfo);
        return buttonInfo.element;
      }
    }

    return null;
  }

  /**
   * 等待按钮变为可用
   * @param {Element} button 按钮元素
   * @param {number} timeout 超时时间（毫秒）
   * @returns {Promise<Element|null>} 可用的按钮或null
   */
  async waitForButtonEnabled(button, timeout = 5000) {
    console.log('⏳ 等待按钮变为可用...');

    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      if (!button.disabled) {
        console.log('✅ 按钮已变为可用');
        return button;
      }

      await new Promise(resolve => setTimeout(resolve, 200));
    }

    console.warn('⏰ 等待按钮可用超时');
    return null;
  }
  
  /**
   * 启用自动模式
   */
  enableAutoMode() {
    this.isAutoMode = true;
    console.log('🤖 自动注入模式已启用');
    
    window.MessageBridge.sendStatusUpdate({
      autoMode: true,
      message: '自动注入模式已启用'
    });
  }
  
  /**
   * 禁用自动模式
   */
  disableAutoMode() {
    this.isAutoMode = false;
    console.log('🔒 自动注入模式已禁用');
    
    window.MessageBridge.sendStatusUpdate({
      autoMode: false,
      message: '自动注入模式已禁用'
    });
  }
  
  /**
   * 切换自动模式
   */
  toggleAutoMode() {
    if (this.isAutoMode) {
      this.disableAutoMode();
    } else {
      this.enableAutoMode();
    }
  }
  
  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getStatus() {
    return {
      isAutoMode: this.isAutoMode,
      pendingActionsCount: this.pendingActions.length,
      lastInjectionTime: this.lastInjectionTime,
      currentSite: this.currentSite ? this.currentSite.name : null
    };
  }

  /**
   * 设置元素值（统一处理不同类型的输入元素）
   * @param {Element} inputElement 输入框元素
   * @param {string} value 要设置的值
   */
  setElementValue(inputElement, value) {
    if (inputElement.tagName.toLowerCase() === 'textarea' || inputElement.tagName.toLowerCase() === 'input') {
      // 对于textarea和input元素
      inputElement.value = value;
    } else if (inputElement.contentEditable === 'true') {
      // 对于可编辑的div元素（如Kimi的Lexical编辑器）
      console.log('📝 处理contenteditable元素...');

      // 检查是否是Lexical编辑器
      if (inputElement.hasAttribute('data-lexical-editor')) {
        console.log('📝 检测到Lexical编辑器，使用特殊处理...');

        // 清空现有内容
        inputElement.innerHTML = '';

        // 创建段落结构，符合Lexical编辑器的格式
        const paragraph = document.createElement('p');
        paragraph.setAttribute('dir', 'ltr');

        const span = document.createElement('span');
        span.setAttribute('data-lexical-text', 'true');
        span.textContent = value;

        paragraph.appendChild(span);
        inputElement.appendChild(paragraph);
      } else {
        // 普通的contenteditable元素
        inputElement.textContent = value;
        inputElement.innerHTML = value.replace(/\n/g, '<br>');
      }
    }
  }

  /**
   * 设置焦点和光标位置
   * @param {Element} inputElement 输入框元素
   * @param {string} value 当前值（用于计算光标位置）
   */
  setFocusAndCursor(inputElement, value) {
    // 设置焦点
    inputElement.focus();

    // 设置光标到末尾
    if (inputElement.setSelectionRange) {
      inputElement.setSelectionRange(value.length, value.length);
    } else if (inputElement.createTextRange) {
      // IE兼容性
      const range = inputElement.createTextRange();
      range.collapse(true);
      range.moveEnd('character', value.length);
      range.moveStart('character', value.length);
      range.select();
    } else if (inputElement.contentEditable === 'true') {
      // 对于contenteditable元素，设置光标到末尾
      const range = document.createRange();
      const selection = window.getSelection();

      // 找到最后一个文本节点
      const walker = document.createTreeWalker(
        inputElement,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      let lastTextNode = null;
      while (walker.nextNode()) {
        lastTextNode = walker.currentNode;
      }

      if (lastTextNode) {
        range.setStart(lastTextNode, lastTextNode.textContent.length);
        range.setEnd(lastTextNode, lastTextNode.textContent.length);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  }
}

// 创建自动注入器实例
const autoInjector = new AutoInjector();

// 导出注入器
window.AutoInjector = autoInjector;

// console.log('💉 自动注入器模块已加载'); // 禁用日志
